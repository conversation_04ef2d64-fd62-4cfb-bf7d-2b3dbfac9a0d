# Override some defaults so BT stack is enabled
# in this example

#
# BT config
#
CONFIG_BT_ENABLED=y
CONFIG_BTDM_CTRL_MODE_BLE_ONLY=y
CONFIG_BTDM_CTRL_MODE_BR_EDR_ONLY=n
CONFIG_BTDM_CTRL_MODE_BTDM=n
CONFIG_BT_BLUEDROID_ENABLED=n
CONFIG_BT_NIMBLE_ENABLED=y
CONFIG_BT_NIMBLE_MAX_CONNECTIONS=1
CONFIG_BT_NIMBLE_MAX_BONDS=1
CONFIG_BT_NIMBLE_MAX_CCCDS=1
CONFIG_BT_NIMBLE_ROLE_PERIPHERAL=n
CONFIG_BT_NIMBLE_ROLE_BROADCASTER=n
CONFIG_BT_NIMBLE_SM_SC=n
CONFIG_BT_NIMBLE_ENABLE_CONN_REATTEMPT=n
CONFIG_BT_NIMBLE_50_FEATURE_SUPPORT=n
CONFIG_BT_NIMBLE_MSYS_1_BLOCK_COUNT=6
CONFIG_BT_NIMBLE_MSYS_2_BLOCK_COUNT=12
CONFIG_BT_NIMBLE_TRANSPORT_ACL_FROM_LL_COUNT=12
CONFIG_BT_NIMBLE_TRANSPORT_EVT_COUNT=15
CONFIG_BT_NIMBLE_ATT_MAX_PREP_ENTRIES=15
CONFIG_BT_NIMBLE_GATT_SERVER=n
CONFIG_BT_NIMBLE_PRINT_ERR_NAME=n
CONFIG_BT_ALARM_MAX_NUM=15

CONFIG_BT_NIMBLE_ATT_PREFERRED_MTU=23
CONFIG_PM_ENABLE=n
CONFIG_HEAP_POISONING_DISABLED=y

CONFIG_BOOTLOADER_LOG_LEVEL_NONE=y
CONFIG_BOOTLOADER_COMPILER_OPTIMIZATION_SIZE=y

CONFIG_VFS_SUPPORT_TERMIOS=n
CONFIG_VFS_SUPPORT_DIR=n
CONFIG_VFS_SUPPORT_IO=n
CONFIG_BT_NIMBLE_HOST_TASK_STACK_SIZE=3152

# FreeRTOS
CONFIG_FREERTOS_HZ=100
CONFIG_FREERTOS_IDLE_TASK_STACKSIZE=1024
CONFIG_FREERTOS_ISR_STACKSIZE=1024
CONFIG_FREERTOS_TIMER_TASK_STACK_DEPTH=2048

# C Library
CONFIG_LIBC_NEWLIB_NANO_FORMAT=y
CONFIG_LIBC_STDIN_LINE_ENDING_CRLF=n
CONFIG_LIBC_STDOUT_LINE_ENDING_CRLF=n

# Optimization
CONFIG_COMPILER_OPTIMIZATION_SIZE=y
CONFIG_COMPILER_OPTIMIZATION_ASSERTIONS_DISABLE=y
CONFIG_COMPILER_CXX_EXCEPTIONS=n

# Heap
CONFIG_HEAP_POISONING_DISABLED=y
CONFIG_HEAP_TRACING=n

# Power Management
CONFIG_PM_ENABLE=n

# No networking
CONFIG_ESP_WIFI_ENABLED=n

# Application
CONFIG_APP_COMPILE_TIME_DATE=n


CONFIG_ESP_COEX_SW_COEXIST_ENABLE=n
CONFIG_ESP_ERR_TO_NAME_LOOKUP=n
CONFIG_SPI_MASTER_ISR_IN_IRAM=n
CONFIG_SPI_SLAVE_ISR_IN_IRAM=n
CONFIG_ESP_GDBSTUB_SUPPORT_TASKS=n
CONFIG_ESP_NETIF_REPORT_DATA_TRAFFIC=n
CONFIG_PM_POWER_DOWN_CPU_IN_LIGHT_SLEEP=n
CONFIG_SPI_FLASH_ROM_DRIVER_PATCH=n
CONFIG_SPI_FLASH_YIELD_DURING_ERASE=n
CONFIG_SPI_FLASH_ENABLE_ENCRYPTED_READ_WRITE=n

#Uncomment below, if security needs to be disabled
#CONFIG_BT_NIMBLE_SECURITY_ENABLE=n


#Uncomment below, if logging needs to be disabled
#CONFIG_LOG_DEFAULT_LEVEL_NONE=y
#CONFIG_LOG_DEFAULT_LEVEL=0

#
# Controller Options
#

CONFIG_BT_CTRL_BLE_MAX_ACT=2
CONFIG_BT_CTRL_ADV_DUP_FILT_MAX=1
CONFIG_BT_CTRL_BLE_ADV_REPORT_FLOW_CTRL_SUPP=n
CONFIG_BT_CTRL_BLE_SCAN_DUPL=n
