{"sources": [{"file": "D:/scanner/blecent/build/CMakeFiles/bootloader"}, {"file": "D:/scanner/blecent/build/CMakeFiles/bootloader.rule"}, {"file": "D:/scanner/blecent/build/CMakeFiles/bootloader-complete.rule"}, {"file": "D:/scanner/blecent/build/bootloader-prefix/src/bootloader-stamp/bootloader-build.rule"}, {"file": "D:/scanner/blecent/build/bootloader-prefix/src/bootloader-stamp/bootloader-configure.rule"}, {"file": "D:/scanner/blecent/build/bootloader-prefix/src/bootloader-stamp/bootloader-download.rule"}, {"file": "D:/scanner/blecent/build/bootloader-prefix/src/bootloader-stamp/bootloader-install.rule"}, {"file": "D:/scanner/blecent/build/bootloader-prefix/src/bootloader-stamp/bootloader-mkdir.rule"}, {"file": "D:/scanner/blecent/build/bootloader-prefix/src/bootloader-stamp/bootloader-patch.rule"}, {"file": "D:/scanner/blecent/build/bootloader-prefix/src/bootloader-stamp/bootloader-update.rule"}], "target": {"labels": ["bootloader"], "name": "bootloader"}}