menu "Example Configuration"

    config EXAMPLE_PEER_ADDR
        string "Peer Address"
        default "ADDR_ANY"
        help
            Enter the peer address in aa:bb:cc:dd:ee:ff form to connect to a specific peripheral

    config EXAMPLE_EXTENDED_ADV
        bool
        depends on SOC_BLE_50_SUPPORTED && BT_NIMBLE_50_FEATURE_SUPPORT
        default y if SOC_ESP_NIMBLE_CONTROLLER
        select BT_NIMBLE_EXT_ADV
        prompt "Enable Extended Adv"
        help
            Use this option to enable extended advertising in the example.
            If this option is disabled, ensure config BT_NIMBLE_EXT_ADV is
            also disabled from Nimble stack menuconfig

    config EXAMPLE_INIT_DEINIT_LOOP
        bool
        prompt "Perform init deinit of nimble stack in a loop"
        help
            Enable this flag, to perform only stack Init and Deinit in a loop.

    config EXAMPLE_ENCRYPTION
        bool
        prompt "Enable Link Encryption"
        help
            This enables bonding and encryption after connection has been established.

    config EXAMPLE_USE_CI_ADDRESS
        bool
        default n
        prompt "Advertise using Test address(Internal Test ONLY)"
        help
            Used for internal test ONLY.
            Use this option to advertise in a specific random address.
endmenu
