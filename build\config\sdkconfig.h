/*
 * Automatically generated file. DO NOT EDIT.
 * Espressif IoT Development Framework (ESP-IDF) 5.4.2 Configuration Header
 */
#pragma once
#define CONFIG_SOC_MPU_MIN_REGION_SIZE 0x20000000
#define CONFIG_SOC_MPU_REGIONS_MAX_NUM 8
#define CONFIG_SOC_ADC_SUPPORTED 1
#define CONFIG_SOC_UART_SUPPORTED 1
#define CONFIG_SOC_PCNT_SUPPORTED 1
#define CONFIG_SOC_PHY_SUPPORTED 1
#define CONFIG_SOC_WIFI_SUPPORTED 1
#define CONFIG_SOC_TWAI_SUPPORTED 1
#define CONFIG_SOC_GDMA_SUPPORTED 1
#define CONFIG_SOC_AHB_GDMA_SUPPORTED 1
#define CONFIG_SOC_GPTIMER_SUPPORTED 1
#define CONFIG_SOC_LCDCAM_SUPPORTED 1
#define CONFIG_SOC_LCDCAM_I80_LCD_SUPPORTED 1
#define CONFIG_SOC_LCDCAM_RGB_LCD_SUPPORTED 1
#define CONFIG_SOC_MCPWM_SUPPORTED 1
#define CONFIG_SOC_DEDICATED_GPIO_SUPPORTED 1
#define CONFIG_SOC_CACHE_SUPPORT_WRAP 1
#define CONFIG_SOC_ULP_SUPPORTED 1
#define CONFIG_SOC_ULP_FSM_SUPPORTED 1
#define CONFIG_SOC_RISCV_COPROC_SUPPORTED 1
#define CONFIG_SOC_BT_SUPPORTED 1
#define CONFIG_SOC_USB_OTG_SUPPORTED 1
#define CONFIG_SOC_USB_SERIAL_JTAG_SUPPORTED 1
#define CONFIG_SOC_CCOMP_TIMER_SUPPORTED 1
#define CONFIG_SOC_ASYNC_MEMCPY_SUPPORTED 1
#define CONFIG_SOC_SUPPORTS_SECURE_DL_MODE 1
#define CONFIG_SOC_EFUSE_KEY_PURPOSE_FIELD 1
#define CONFIG_SOC_EFUSE_SUPPORTED 1
#define CONFIG_SOC_SDMMC_HOST_SUPPORTED 1
#define CONFIG_SOC_RTC_FAST_MEM_SUPPORTED 1
#define CONFIG_SOC_RTC_SLOW_MEM_SUPPORTED 1
#define CONFIG_SOC_RTC_MEM_SUPPORTED 1
#define CONFIG_SOC_PSRAM_DMA_CAPABLE 1
#define CONFIG_SOC_XT_WDT_SUPPORTED 1
#define CONFIG_SOC_I2S_SUPPORTED 1
#define CONFIG_SOC_RMT_SUPPORTED 1
#define CONFIG_SOC_SDM_SUPPORTED 1
#define CONFIG_SOC_GPSPI_SUPPORTED 1
#define CONFIG_SOC_LEDC_SUPPORTED 1
#define CONFIG_SOC_I2C_SUPPORTED 1
#define CONFIG_SOC_SYSTIMER_SUPPORTED 1
#define CONFIG_SOC_SUPPORT_COEXISTENCE 1
#define CONFIG_SOC_TEMP_SENSOR_SUPPORTED 1
#define CONFIG_SOC_AES_SUPPORTED 1
#define CONFIG_SOC_MPI_SUPPORTED 1
#define CONFIG_SOC_SHA_SUPPORTED 1
#define CONFIG_SOC_HMAC_SUPPORTED 1
#define CONFIG_SOC_DIG_SIGN_SUPPORTED 1
#define CONFIG_SOC_FLASH_ENC_SUPPORTED 1
#define CONFIG_SOC_SECURE_BOOT_SUPPORTED 1
#define CONFIG_SOC_MEMPROT_SUPPORTED 1
#define CONFIG_SOC_TOUCH_SENSOR_SUPPORTED 1
#define CONFIG_SOC_BOD_SUPPORTED 1
#define CONFIG_SOC_CLK_TREE_SUPPORTED 1
#define CONFIG_SOC_MPU_SUPPORTED 1
#define CONFIG_SOC_WDT_SUPPORTED 1
#define CONFIG_SOC_SPI_FLASH_SUPPORTED 1
#define CONFIG_SOC_RNG_SUPPORTED 1
#define CONFIG_SOC_LIGHT_SLEEP_SUPPORTED 1
#define CONFIG_SOC_DEEP_SLEEP_SUPPORTED 1
#define CONFIG_SOC_LP_PERIPH_SHARE_INTERRUPT 1
#define CONFIG_SOC_PM_SUPPORTED 1
#define CONFIG_SOC_SIMD_INSTRUCTION_SUPPORTED 1
#define CONFIG_SOC_XTAL_SUPPORT_40M 1
#define CONFIG_SOC_APPCPU_HAS_CLOCK_GATING_BUG 1
#define CONFIG_SOC_ADC_RTC_CTRL_SUPPORTED 1
#define CONFIG_SOC_ADC_DIG_CTRL_SUPPORTED 1
#define CONFIG_SOC_ADC_ARBITER_SUPPORTED 1
#define CONFIG_SOC_ADC_DIG_IIR_FILTER_SUPPORTED 1
#define CONFIG_SOC_ADC_MONITOR_SUPPORTED 1
#define CONFIG_SOC_ADC_DMA_SUPPORTED 1
#define CONFIG_SOC_ADC_PERIPH_NUM 2
#define CONFIG_SOC_ADC_MAX_CHANNEL_NUM 10
#define CONFIG_SOC_ADC_ATTEN_NUM 4
#define CONFIG_SOC_ADC_DIGI_CONTROLLER_NUM 2
#define CONFIG_SOC_ADC_PATT_LEN_MAX 24
#define CONFIG_SOC_ADC_DIGI_MIN_BITWIDTH 12
#define CONFIG_SOC_ADC_DIGI_MAX_BITWIDTH 12
#define CONFIG_SOC_ADC_DIGI_RESULT_BYTES 4
#define CONFIG_SOC_ADC_DIGI_DATA_BYTES_PER_CONV 4
#define CONFIG_SOC_ADC_DIGI_IIR_FILTER_NUM 2
#define CONFIG_SOC_ADC_DIGI_MONITOR_NUM 2
#define CONFIG_SOC_ADC_SAMPLE_FREQ_THRES_HIGH 83333
#define CONFIG_SOC_ADC_SAMPLE_FREQ_THRES_LOW 611
#define CONFIG_SOC_ADC_RTC_MIN_BITWIDTH 12
#define CONFIG_SOC_ADC_RTC_MAX_BITWIDTH 12
#define CONFIG_SOC_ADC_CALIBRATION_V1_SUPPORTED 1
#define CONFIG_SOC_ADC_SELF_HW_CALI_SUPPORTED 1
#define CONFIG_SOC_ADC_SHARED_POWER 1
#define CONFIG_SOC_APB_BACKUP_DMA 1
#define CONFIG_SOC_BROWNOUT_RESET_SUPPORTED 1
#define CONFIG_SOC_CACHE_WRITEBACK_SUPPORTED 1
#define CONFIG_SOC_CACHE_FREEZE_SUPPORTED 1
#define CONFIG_SOC_CPU_CORES_NUM 2
#define CONFIG_SOC_CPU_INTR_NUM 32
#define CONFIG_SOC_CPU_HAS_FPU 1
#define CONFIG_SOC_HP_CPU_HAS_MULTIPLE_CORES 1
#define CONFIG_SOC_CPU_BREAKPOINTS_NUM 2
#define CONFIG_SOC_CPU_WATCHPOINTS_NUM 2
#define CONFIG_SOC_CPU_WATCHPOINT_MAX_REGION_SIZE 64
#define CONFIG_SOC_SIMD_PREFERRED_DATA_ALIGNMENT 16
#define CONFIG_SOC_DS_SIGNATURE_MAX_BIT_LEN 4096
#define CONFIG_SOC_DS_KEY_PARAM_MD_IV_LENGTH 16
#define CONFIG_SOC_DS_KEY_CHECK_MAX_WAIT_US 1100
#define CONFIG_SOC_AHB_GDMA_VERSION 1
#define CONFIG_SOC_GDMA_NUM_GROUPS_MAX 1
#define CONFIG_SOC_GDMA_PAIRS_PER_GROUP 5
#define CONFIG_SOC_GDMA_PAIRS_PER_GROUP_MAX 5
#define CONFIG_SOC_AHB_GDMA_SUPPORT_PSRAM 1
#define CONFIG_SOC_GPIO_PORT 1
#define CONFIG_SOC_GPIO_PIN_COUNT 49
#define CONFIG_SOC_GPIO_SUPPORT_PIN_GLITCH_FILTER 1
#define CONFIG_SOC_GPIO_FILTER_CLK_SUPPORT_APB 1
#define CONFIG_SOC_GPIO_SUPPORT_RTC_INDEPENDENT 1
#define CONFIG_SOC_GPIO_SUPPORT_FORCE_HOLD 1
#define CONFIG_SOC_GPIO_VALID_GPIO_MASK 0x1FFFFFFFFFFFF
#define CONFIG_SOC_GPIO_IN_RANGE_MAX 48
#define CONFIG_SOC_GPIO_OUT_RANGE_MAX 48
#define CONFIG_SOC_GPIO_VALID_DIGITAL_IO_PAD_MASK 0x0001FFFFFC000000
#define CONFIG_SOC_GPIO_CLOCKOUT_BY_IO_MUX 1
#define CONFIG_SOC_GPIO_CLOCKOUT_CHANNEL_NUM 3
#define CONFIG_SOC_GPIO_SUPPORT_HOLD_IO_IN_DSLP 1
#define CONFIG_SOC_DEDIC_GPIO_OUT_CHANNELS_NUM 8
#define CONFIG_SOC_DEDIC_GPIO_IN_CHANNELS_NUM 8
#define CONFIG_SOC_DEDIC_GPIO_OUT_AUTO_ENABLE 1
#define CONFIG_SOC_I2C_NUM 2
#define CONFIG_SOC_HP_I2C_NUM 2
#define CONFIG_SOC_I2C_FIFO_LEN 32
#define CONFIG_SOC_I2C_CMD_REG_NUM 8
#define CONFIG_SOC_I2C_SUPPORT_SLAVE 1
#define CONFIG_SOC_I2C_SUPPORT_HW_CLR_BUS 1
#define CONFIG_SOC_I2C_SUPPORT_XTAL 1
#define CONFIG_SOC_I2C_SUPPORT_RTC 1
#define CONFIG_SOC_I2C_SUPPORT_10BIT_ADDR 1
#define CONFIG_SOC_I2C_SLAVE_SUPPORT_BROADCAST 1
#define CONFIG_SOC_I2C_SLAVE_SUPPORT_I2CRAM_ACCESS 1
#define CONFIG_SOC_I2C_SLAVE_CAN_GET_STRETCH_CAUSE 1
#define CONFIG_SOC_I2S_NUM 2
#define CONFIG_SOC_I2S_HW_VERSION_2 1
#define CONFIG_SOC_I2S_SUPPORTS_XTAL 1
#define CONFIG_SOC_I2S_SUPPORTS_PLL_F160M 1
#define CONFIG_SOC_I2S_SUPPORTS_PCM 1
#define CONFIG_SOC_I2S_SUPPORTS_PDM 1
#define CONFIG_SOC_I2S_SUPPORTS_PDM_TX 1
#define CONFIG_SOC_I2S_PDM_MAX_TX_LINES 2
#define CONFIG_SOC_I2S_SUPPORTS_PDM_RX 1
#define CONFIG_SOC_I2S_PDM_MAX_RX_LINES 4
#define CONFIG_SOC_I2S_SUPPORTS_TDM 1
#define CONFIG_SOC_LEDC_SUPPORT_APB_CLOCK 1
#define CONFIG_SOC_LEDC_SUPPORT_XTAL_CLOCK 1
#define CONFIG_SOC_LEDC_TIMER_NUM 4
#define CONFIG_SOC_LEDC_CHANNEL_NUM 8
#define CONFIG_SOC_LEDC_TIMER_BIT_WIDTH 14
#define CONFIG_SOC_LEDC_SUPPORT_FADE_STOP 1
#define CONFIG_SOC_MCPWM_GROUPS 2
#define CONFIG_SOC_MCPWM_TIMERS_PER_GROUP 3
#define CONFIG_SOC_MCPWM_OPERATORS_PER_GROUP 3
#define CONFIG_SOC_MCPWM_COMPARATORS_PER_OPERATOR 2
#define CONFIG_SOC_MCPWM_GENERATORS_PER_OPERATOR 2
#define CONFIG_SOC_MCPWM_TRIGGERS_PER_OPERATOR 2
#define CONFIG_SOC_MCPWM_GPIO_FAULTS_PER_GROUP 3
#define CONFIG_SOC_MCPWM_CAPTURE_TIMERS_PER_GROUP 1
#define CONFIG_SOC_MCPWM_CAPTURE_CHANNELS_PER_TIMER 3
#define CONFIG_SOC_MCPWM_GPIO_SYNCHROS_PER_GROUP 3
#define CONFIG_SOC_MCPWM_SWSYNC_CAN_PROPAGATE 1
#define CONFIG_SOC_MMU_LINEAR_ADDRESS_REGION_NUM 1
#define CONFIG_SOC_MMU_PERIPH_NUM 1
#define CONFIG_SOC_PCNT_GROUPS 1
#define CONFIG_SOC_PCNT_UNITS_PER_GROUP 4
#define CONFIG_SOC_PCNT_CHANNELS_PER_UNIT 2
#define CONFIG_SOC_PCNT_THRES_POINT_PER_UNIT 2
#define CONFIG_SOC_RMT_GROUPS 1
#define CONFIG_SOC_RMT_TX_CANDIDATES_PER_GROUP 4
#define CONFIG_SOC_RMT_RX_CANDIDATES_PER_GROUP 4
#define CONFIG_SOC_RMT_CHANNELS_PER_GROUP 8
#define CONFIG_SOC_RMT_MEM_WORDS_PER_CHANNEL 48
#define CONFIG_SOC_RMT_SUPPORT_RX_PINGPONG 1
#define CONFIG_SOC_RMT_SUPPORT_RX_DEMODULATION 1
#define CONFIG_SOC_RMT_SUPPORT_TX_ASYNC_STOP 1
#define CONFIG_SOC_RMT_SUPPORT_TX_LOOP_COUNT 1
#define CONFIG_SOC_RMT_SUPPORT_TX_LOOP_AUTO_STOP 1
#define CONFIG_SOC_RMT_SUPPORT_TX_SYNCHRO 1
#define CONFIG_SOC_RMT_SUPPORT_TX_CARRIER_DATA_ONLY 1
#define CONFIG_SOC_RMT_SUPPORT_XTAL 1
#define CONFIG_SOC_RMT_SUPPORT_RC_FAST 1
#define CONFIG_SOC_RMT_SUPPORT_APB 1
#define CONFIG_SOC_RMT_SUPPORT_DMA 1
#define CONFIG_SOC_LCD_I80_SUPPORTED 1
#define CONFIG_SOC_LCD_RGB_SUPPORTED 1
#define CONFIG_SOC_LCD_I80_BUSES 1
#define CONFIG_SOC_LCD_RGB_PANELS 1
#define CONFIG_SOC_LCD_I80_BUS_WIDTH 16
#define CONFIG_SOC_LCD_RGB_DATA_WIDTH 16
#define CONFIG_SOC_LCD_SUPPORT_RGB_YUV_CONV 1
#define CONFIG_SOC_LCDCAM_I80_NUM_BUSES 1
#define CONFIG_SOC_LCDCAM_I80_BUS_WIDTH 16
#define CONFIG_SOC_LCDCAM_RGB_NUM_PANELS 1
#define CONFIG_SOC_LCDCAM_RGB_DATA_WIDTH 16
#define CONFIG_SOC_RTC_CNTL_CPU_PD_DMA_BUS_WIDTH 128
#define CONFIG_SOC_RTC_CNTL_CPU_PD_REG_FILE_NUM 549
#define CONFIG_SOC_RTC_CNTL_TAGMEM_PD_DMA_BUS_WIDTH 128
#define CONFIG_SOC_RTCIO_PIN_COUNT 22
#define CONFIG_SOC_RTCIO_INPUT_OUTPUT_SUPPORTED 1
#define CONFIG_SOC_RTCIO_HOLD_SUPPORTED 1
#define CONFIG_SOC_RTCIO_WAKE_SUPPORTED 1
#define CONFIG_SOC_LP_IO_CLOCK_IS_INDEPENDENT 1
#define CONFIG_SOC_SDM_GROUPS 1
#define CONFIG_SOC_SDM_CHANNELS_PER_GROUP 8
#define CONFIG_SOC_SDM_CLK_SUPPORT_APB 1
#define CONFIG_SOC_SPI_PERIPH_NUM 3
#define CONFIG_SOC_SPI_MAX_CS_NUM 6
#define CONFIG_SOC_SPI_MAXIMUM_BUFFER_SIZE 64
#define CONFIG_SOC_SPI_SUPPORT_DDRCLK 1
#define CONFIG_SOC_SPI_SLAVE_SUPPORT_SEG_TRANS 1
#define CONFIG_SOC_SPI_SUPPORT_CD_SIG 1
#define CONFIG_SOC_SPI_SUPPORT_CONTINUOUS_TRANS 1
#define CONFIG_SOC_SPI_SUPPORT_SLAVE_HD_VER2 1
#define CONFIG_SOC_SPI_SUPPORT_CLK_APB 1
#define CONFIG_SOC_SPI_SUPPORT_CLK_XTAL 1
#define CONFIG_SOC_SPI_PERIPH_SUPPORT_CONTROL_DUMMY_OUT 1
#define CONFIG_SOC_MEMSPI_IS_INDEPENDENT 1
#define CONFIG_SOC_SPI_MAX_PRE_DIVIDER 16
#define CONFIG_SOC_SPI_SUPPORT_OCT 1
#define CONFIG_SOC_SPI_SCT_SUPPORTED 1
#define CONFIG_SOC_SPI_SCT_REG_NUM 14
#define CONFIG_SOC_SPI_SCT_BUFFER_NUM_MAX 1
#define CONFIG_SOC_SPI_SCT_CONF_BITLEN_MAX 0x3FFFA
#define CONFIG_SOC_MEMSPI_SRC_FREQ_120M 1
#define CONFIG_SOC_MEMSPI_SRC_FREQ_80M_SUPPORTED 1
#define CONFIG_SOC_MEMSPI_SRC_FREQ_40M_SUPPORTED 1
#define CONFIG_SOC_MEMSPI_SRC_FREQ_20M_SUPPORTED 1
#define CONFIG_SOC_SPIRAM_SUPPORTED 1
#define CONFIG_SOC_SPIRAM_XIP_SUPPORTED 1
#define CONFIG_SOC_SYSTIMER_COUNTER_NUM 2
#define CONFIG_SOC_SYSTIMER_ALARM_NUM 3
#define CONFIG_SOC_SYSTIMER_BIT_WIDTH_LO 32
#define CONFIG_SOC_SYSTIMER_BIT_WIDTH_HI 20
#define CONFIG_SOC_SYSTIMER_FIXED_DIVIDER 1
#define CONFIG_SOC_SYSTIMER_INT_LEVEL 1
#define CONFIG_SOC_SYSTIMER_ALARM_MISS_COMPENSATE 1
#define CONFIG_SOC_TIMER_GROUPS 2
#define CONFIG_SOC_TIMER_GROUP_TIMERS_PER_GROUP 2
#define CONFIG_SOC_TIMER_GROUP_COUNTER_BIT_WIDTH 54
#define CONFIG_SOC_TIMER_GROUP_SUPPORT_XTAL 1
#define CONFIG_SOC_TIMER_GROUP_SUPPORT_APB 1
#define CONFIG_SOC_TIMER_GROUP_TOTAL_TIMERS 4
#define CONFIG_SOC_LP_TIMER_BIT_WIDTH_LO 32
#define CONFIG_SOC_LP_TIMER_BIT_WIDTH_HI 16
#define CONFIG_SOC_TOUCH_SENSOR_VERSION 2
#define CONFIG_SOC_TOUCH_SENSOR_NUM 15
#define CONFIG_SOC_TOUCH_SUPPORT_SLEEP_WAKEUP 1
#define CONFIG_SOC_TOUCH_SUPPORT_WATERPROOF 1
#define CONFIG_SOC_TOUCH_SUPPORT_PROX_SENSING 1
#define CONFIG_SOC_TOUCH_PROXIMITY_CHANNEL_NUM 3
#define CONFIG_SOC_TOUCH_PROXIMITY_MEAS_DONE_SUPPORTED 1
#define CONFIG_SOC_TOUCH_SAMPLE_CFG_NUM 1
#define CONFIG_SOC_TWAI_CONTROLLER_NUM 1
#define CONFIG_SOC_TWAI_CLK_SUPPORT_APB 1
#define CONFIG_SOC_TWAI_BRP_MIN 2
#define CONFIG_SOC_TWAI_BRP_MAX 16384
#define CONFIG_SOC_TWAI_SUPPORTS_RX_STATUS 1
#define CONFIG_SOC_UART_NUM 3
#define CONFIG_SOC_UART_HP_NUM 3
#define CONFIG_SOC_UART_FIFO_LEN 128
#define CONFIG_SOC_UART_BITRATE_MAX 5000000
#define CONFIG_SOC_UART_SUPPORT_FSM_TX_WAIT_SEND 1
#define CONFIG_SOC_UART_SUPPORT_WAKEUP_INT 1
#define CONFIG_SOC_UART_SUPPORT_APB_CLK 1
#define CONFIG_SOC_UART_SUPPORT_RTC_CLK 1
#define CONFIG_SOC_UART_SUPPORT_XTAL_CLK 1
#define CONFIG_SOC_USB_OTG_PERIPH_NUM 1
#define CONFIG_SOC_SHA_DMA_MAX_BUFFER_SIZE 3968
#define CONFIG_SOC_SHA_SUPPORT_DMA 1
#define CONFIG_SOC_SHA_SUPPORT_RESUME 1
#define CONFIG_SOC_SHA_GDMA 1
#define CONFIG_SOC_SHA_SUPPORT_SHA1 1
#define CONFIG_SOC_SHA_SUPPORT_SHA224 1
#define CONFIG_SOC_SHA_SUPPORT_SHA256 1
#define CONFIG_SOC_SHA_SUPPORT_SHA384 1
#define CONFIG_SOC_SHA_SUPPORT_SHA512 1
#define CONFIG_SOC_SHA_SUPPORT_SHA512_224 1
#define CONFIG_SOC_SHA_SUPPORT_SHA512_256 1
#define CONFIG_SOC_SHA_SUPPORT_SHA512_T 1
#define CONFIG_SOC_MPI_MEM_BLOCKS_NUM 4
#define CONFIG_SOC_MPI_OPERATIONS_NUM 3
#define CONFIG_SOC_RSA_MAX_BIT_LEN 4096
#define CONFIG_SOC_AES_SUPPORT_DMA 1
#define CONFIG_SOC_AES_GDMA 1
#define CONFIG_SOC_AES_SUPPORT_AES_128 1
#define CONFIG_SOC_AES_SUPPORT_AES_256 1
#define CONFIG_SOC_PM_SUPPORT_EXT0_WAKEUP 1
#define CONFIG_SOC_PM_SUPPORT_EXT1_WAKEUP 1
#define CONFIG_SOC_PM_SUPPORT_EXT_WAKEUP 1
#define CONFIG_SOC_PM_SUPPORT_WIFI_WAKEUP 1
#define CONFIG_SOC_PM_SUPPORT_BT_WAKEUP 1
#define CONFIG_SOC_PM_SUPPORT_TOUCH_SENSOR_WAKEUP 1
#define CONFIG_SOC_PM_SUPPORT_CPU_PD 1
#define CONFIG_SOC_PM_SUPPORT_TAGMEM_PD 1
#define CONFIG_SOC_PM_SUPPORT_RTC_PERIPH_PD 1
#define CONFIG_SOC_PM_SUPPORT_RC_FAST_PD 1
#define CONFIG_SOC_PM_SUPPORT_VDDSDIO_PD 1
#define CONFIG_SOC_PM_SUPPORT_MAC_BB_PD 1
#define CONFIG_SOC_PM_SUPPORT_MODEM_PD 1
#define CONFIG_SOC_CONFIGURABLE_VDDSDIO_SUPPORTED 1
#define CONFIG_SOC_PM_SUPPORT_DEEPSLEEP_CHECK_STUB_ONLY 1
#define CONFIG_SOC_PM_CPU_RETENTION_BY_RTCCNTL 1
#define CONFIG_SOC_PM_MODEM_RETENTION_BY_BACKUPDMA 1
#define CONFIG_SOC_PM_MODEM_PD_BY_SW 1
#define CONFIG_SOC_CLK_RC_FAST_D256_SUPPORTED 1
#define CONFIG_SOC_RTC_SLOW_CLK_SUPPORT_RC_FAST_D256 1
#define CONFIG_SOC_CLK_RC_FAST_SUPPORT_CALIBRATION 1
#define CONFIG_SOC_CLK_XTAL32K_SUPPORTED 1
#define CONFIG_SOC_EFUSE_DIS_DOWNLOAD_ICACHE 1
#define CONFIG_SOC_EFUSE_DIS_DOWNLOAD_DCACHE 1
#define CONFIG_SOC_EFUSE_HARD_DIS_JTAG 1
#define CONFIG_SOC_EFUSE_DIS_USB_JTAG 1
#define CONFIG_SOC_EFUSE_SOFT_DIS_JTAG 1
#define CONFIG_SOC_EFUSE_DIS_DIRECT_BOOT 1
#define CONFIG_SOC_EFUSE_DIS_ICACHE 1
#define CONFIG_SOC_EFUSE_BLOCK9_KEY_PURPOSE_QUIRK 1
#define CONFIG_SOC_SECURE_BOOT_V2_RSA 1
#define CONFIG_SOC_EFUSE_SECURE_BOOT_KEY_DIGESTS 3
#define CONFIG_SOC_EFUSE_REVOKE_BOOT_KEY_DIGESTS 1
#define CONFIG_SOC_SUPPORT_SECURE_BOOT_REVOKE_KEY 1
#define CONFIG_SOC_FLASH_ENCRYPTED_XTS_AES_BLOCK_MAX 64
#define CONFIG_SOC_FLASH_ENCRYPTION_XTS_AES 1
#define CONFIG_SOC_FLASH_ENCRYPTION_XTS_AES_OPTIONS 1
#define CONFIG_SOC_FLASH_ENCRYPTION_XTS_AES_128 1
#define CONFIG_SOC_FLASH_ENCRYPTION_XTS_AES_256 1
#define CONFIG_SOC_MEMPROT_CPU_PREFETCH_PAD_SIZE 16
#define CONFIG_SOC_MEMPROT_MEM_ALIGN_SIZE 256
#define CONFIG_SOC_PHY_DIG_REGS_MEM_SIZE 21
#define CONFIG_SOC_MAC_BB_PD_MEM_SIZE 192
#define CONFIG_SOC_WIFI_LIGHT_SLEEP_CLK_WIDTH 12
#define CONFIG_SOC_SPI_MEM_SUPPORT_AUTO_WAIT_IDLE 1
#define CONFIG_SOC_SPI_MEM_SUPPORT_AUTO_SUSPEND 1
#define CONFIG_SOC_SPI_MEM_SUPPORT_AUTO_RESUME 1
#define CONFIG_SOC_SPI_MEM_SUPPORT_SW_SUSPEND 1
#define CONFIG_SOC_SPI_MEM_SUPPORT_OPI_MODE 1
#define CONFIG_SOC_SPI_MEM_SUPPORT_TIMING_TUNING 1
#define CONFIG_SOC_SPI_MEM_SUPPORT_CONFIG_GPIO_BY_EFUSE 1
#define CONFIG_SOC_SPI_MEM_SUPPORT_WRAP 1
#define CONFIG_SOC_MEMSPI_TIMING_TUNING_BY_MSPI_DELAY 1
#define CONFIG_SOC_MEMSPI_CORE_CLK_SHARED_WITH_PSRAM 1
#define CONFIG_SOC_SPI_MEM_SUPPORT_CACHE_32BIT_ADDR_MAP 1
#define CONFIG_SOC_COEX_HW_PTI 1
#define CONFIG_SOC_EXTERNAL_COEX_LEADER_TX_LINE 1
#define CONFIG_SOC_SDMMC_USE_GPIO_MATRIX 1
#define CONFIG_SOC_SDMMC_NUM_SLOTS 2
#define CONFIG_SOC_SDMMC_SUPPORT_XTAL_CLOCK 1
#define CONFIG_SOC_SDMMC_DELAY_PHASE_NUM 4
#define CONFIG_SOC_TEMPERATURE_SENSOR_SUPPORT_FAST_RC 1
#define CONFIG_SOC_WIFI_HW_TSF 1
#define CONFIG_SOC_WIFI_FTM_SUPPORT 1
#define CONFIG_SOC_WIFI_GCMP_SUPPORT 1
#define CONFIG_SOC_WIFI_WAPI_SUPPORT 1
#define CONFIG_SOC_WIFI_CSI_SUPPORT 1
#define CONFIG_SOC_WIFI_MESH_SUPPORT 1
#define CONFIG_SOC_WIFI_SUPPORT_VARIABLE_BEACON_WINDOW 1
#define CONFIG_SOC_WIFI_PHY_NEEDS_USB_WORKAROUND 1
#define CONFIG_SOC_BLE_SUPPORTED 1
#define CONFIG_SOC_BLE_MESH_SUPPORTED 1
#define CONFIG_SOC_BLE_50_SUPPORTED 1
#define CONFIG_SOC_BLE_DEVICE_PRIVACY_SUPPORTED 1
#define CONFIG_SOC_BLUFI_SUPPORTED 1
#define CONFIG_SOC_ULP_HAS_ADC 1
#define CONFIG_SOC_PHY_COMBO_MODULE 1
#define CONFIG_IDF_CMAKE 1
#define CONFIG_IDF_TOOLCHAIN "gcc"
#define CONFIG_IDF_TOOLCHAIN_GCC 1
#define CONFIG_IDF_TARGET_ARCH_XTENSA 1
#define CONFIG_IDF_TARGET_ARCH "xtensa"
#define CONFIG_IDF_TARGET "esp32s3"
#define CONFIG_IDF_INIT_VERSION "5.4.2"
#define CONFIG_IDF_TARGET_ESP32S3 1
#define CONFIG_IDF_FIRMWARE_CHIP_ID 0x0009
#define CONFIG_APP_BUILD_TYPE_APP_2NDBOOT 1
#define CONFIG_APP_BUILD_GENERATE_BINARIES 1
#define CONFIG_APP_BUILD_BOOTLOADER 1
#define CONFIG_APP_BUILD_USE_FLASH_SECTIONS 1
#define CONFIG_BOOTLOADER_COMPILE_TIME_DATE 1
#define CONFIG_BOOTLOADER_PROJECT_VER 1
#define CONFIG_BOOTLOADER_OFFSET_IN_FLASH 0x0
#define CONFIG_BOOTLOADER_COMPILER_OPTIMIZATION_SIZE 1
#define CONFIG_BOOTLOADER_LOG_LEVEL_INFO 1
#define CONFIG_BOOTLOADER_LOG_LEVEL 3
#define CONFIG_BOOTLOADER_LOG_TIMESTAMP_SOURCE_CPU_TICKS 1
#define CONFIG_BOOTLOADER_FLASH_XMC_SUPPORT 1
#define CONFIG_BOOTLOADER_VDDSDIO_BOOST_1_9V 1
#define CONFIG_BOOTLOADER_REGION_PROTECTION_ENABLE 1
#define CONFIG_BOOTLOADER_WDT_ENABLE 1
#define CONFIG_BOOTLOADER_WDT_TIME_MS 9000
#define CONFIG_BOOTLOADER_RESERVE_RTC_SIZE 0x0
#define CONFIG_SECURE_BOOT_V2_RSA_SUPPORTED 1
#define CONFIG_SECURE_BOOT_V2_PREFERRED 1
#define CONFIG_SECURE_ROM_DL_MODE_ENABLED 1
#define CONFIG_APP_COMPILE_TIME_DATE 1
#define CONFIG_APP_RETRIEVE_LEN_ELF_SHA 9
#define CONFIG_ESP_ROM_HAS_CRC_LE 1
#define CONFIG_ESP_ROM_HAS_CRC_BE 1
#define CONFIG_ESP_ROM_HAS_MZ_CRC32 1
#define CONFIG_ESP_ROM_HAS_JPEG_DECODE 1
#define CONFIG_ESP_ROM_UART_CLK_IS_XTAL 1
#define CONFIG_ESP_ROM_HAS_RETARGETABLE_LOCKING 1
#define CONFIG_ESP_ROM_USB_OTG_NUM 3
#define CONFIG_ESP_ROM_USB_SERIAL_DEVICE_NUM 4
#define CONFIG_ESP_ROM_HAS_ERASE_0_REGION_BUG 1
#define CONFIG_ESP_ROM_HAS_ENCRYPTED_WRITES_USING_LEGACY_DRV 1
#define CONFIG_ESP_ROM_GET_CLK_FREQ 1
#define CONFIG_ESP_ROM_HAS_HAL_WDT 1
#define CONFIG_ESP_ROM_NEEDS_SWSETUP_WORKAROUND 1
#define CONFIG_ESP_ROM_HAS_LAYOUT_TABLE 1
#define CONFIG_ESP_ROM_HAS_SPI_FLASH 1
#define CONFIG_ESP_ROM_HAS_ETS_PRINTF_BUG 1
#define CONFIG_ESP_ROM_HAS_NEWLIB 1
#define CONFIG_ESP_ROM_HAS_NEWLIB_NANO_FORMAT 1
#define CONFIG_ESP_ROM_HAS_NEWLIB_32BIT_TIME 1
#define CONFIG_ESP_ROM_NEEDS_SET_CACHE_MMU_SIZE 1
#define CONFIG_ESP_ROM_RAM_APP_NEEDS_MMU_INIT 1
#define CONFIG_ESP_ROM_HAS_FLASH_COUNT_PAGES_BUG 1
#define CONFIG_ESP_ROM_HAS_CACHE_SUSPEND_WAITI_BUG 1
#define CONFIG_ESP_ROM_HAS_CACHE_WRITEBACK_BUG 1
#define CONFIG_ESP_ROM_HAS_SW_FLOAT 1
#define CONFIG_ESP_ROM_HAS_VERSION 1
#define CONFIG_ESP_ROM_SUPPORT_DEEP_SLEEP_WAKEUP_STUB 1
#define CONFIG_ESP_ROM_HAS_OUTPUT_PUTC_FUNC 1
#define CONFIG_BOOT_ROM_LOG_ALWAYS_ON 1
#define CONFIG_ESPTOOLPY_FLASH_MODE_AUTO_DETECT 1
#define CONFIG_ESPTOOLPY_FLASHMODE_DIO 1
#define CONFIG_ESPTOOLPY_FLASH_SAMPLE_MODE_STR 1
#define CONFIG_ESPTOOLPY_FLASHMODE "dio"
#define CONFIG_ESPTOOLPY_FLASHFREQ_80M 1
#define CONFIG_ESPTOOLPY_FLASHFREQ "80m"
#define CONFIG_ESPTOOLPY_FLASHSIZE_2MB 1
#define CONFIG_ESPTOOLPY_FLASHSIZE "2MB"
#define CONFIG_ESPTOOLPY_BEFORE_RESET 1
#define CONFIG_ESPTOOLPY_BEFORE "default_reset"
#define CONFIG_ESPTOOLPY_AFTER_RESET 1
#define CONFIG_ESPTOOLPY_AFTER "hard_reset"
#define CONFIG_ESPTOOLPY_MONITOR_BAUD 115200
#define CONFIG_PARTITION_TABLE_SINGLE_APP 1
#define CONFIG_PARTITION_TABLE_CUSTOM_FILENAME "partitions.csv"
#define CONFIG_PARTITION_TABLE_FILENAME "partitions_singleapp.csv"
#define CONFIG_PARTITION_TABLE_OFFSET 0x8000
#define CONFIG_PARTITION_TABLE_MD5 1
#define CONFIG_EXAMPLE_PEER_ADDR "ADDR_ANY"
#define CONFIG_COMPILER_OPTIMIZATION_DEBUG 1
#define CONFIG_COMPILER_OPTIMIZATION_ASSERTIONS_ENABLE 1
#define CONFIG_COMPILER_ASSERT_NDEBUG_EVALUATE 1
#define CONFIG_COMPILER_FLOAT_LIB_FROM_GCCLIB 1
#define CONFIG_COMPILER_OPTIMIZATION_ASSERTION_LEVEL 2
#define CONFIG_COMPILER_HIDE_PATHS_MACROS 1
#define CONFIG_COMPILER_STACK_CHECK_MODE_NONE 1
#define CONFIG_COMPILER_DISABLE_DEFAULT_ERRORS 1
#define CONFIG_COMPILER_RT_LIB_GCCLIB 1
#define CONFIG_COMPILER_RT_LIB_NAME "gcc"
#define CONFIG_COMPILER_ORPHAN_SECTIONS_WARNING 1
#define CONFIG_APPTRACE_DEST_NONE 1
#define CONFIG_APPTRACE_DEST_UART_NONE 1
#define CONFIG_APPTRACE_UART_TASK_PRIO 1
#define CONFIG_APPTRACE_LOCK_ENABLE 1
#define CONFIG_BT_ENABLED 1
#define CONFIG_BT_NIMBLE_ENABLED 1
#define CONFIG_BT_CONTROLLER_ENABLED 1
#define CONFIG_BT_NIMBLE_MEM_ALLOC_MODE_INTERNAL 1
#define CONFIG_BT_NIMBLE_LOG_LEVEL_INFO 1
#define CONFIG_BT_NIMBLE_LOG_LEVEL 1
#define CONFIG_BT_NIMBLE_MAX_CONNECTIONS 3
#define CONFIG_BT_NIMBLE_MAX_BONDS 3
#define CONFIG_BT_NIMBLE_MAX_CCCDS 8
#define CONFIG_BT_NIMBLE_L2CAP_COC_MAX_NUM 0
#define CONFIG_BT_NIMBLE_PINNED_TO_CORE_0 1
#define CONFIG_BT_NIMBLE_PINNED_TO_CORE 0
#define CONFIG_BT_NIMBLE_HOST_TASK_STACK_SIZE 4096
#define CONFIG_BT_NIMBLE_ROLE_CENTRAL 1
#define CONFIG_BT_NIMBLE_ROLE_PERIPHERAL 1
#define CONFIG_BT_NIMBLE_ROLE_BROADCASTER 1
#define CONFIG_BT_NIMBLE_ROLE_OBSERVER 1
#define CONFIG_BT_NIMBLE_GATT_CLIENT 1
#define CONFIG_BT_NIMBLE_GATT_SERVER 1
#define CONFIG_BT_NIMBLE_SECURITY_ENABLE 1
#define CONFIG_BT_NIMBLE_SM_LEGACY 1
#define CONFIG_BT_NIMBLE_SM_SC 1
#define CONFIG_BT_NIMBLE_LL_CFG_FEAT_LE_ENCRYPTION 1
#define CONFIG_BT_NIMBLE_SM_LVL 0
#define CONFIG_BT_NIMBLE_SM_SC_ONLY 0
#define CONFIG_BT_NIMBLE_PRINT_ERR_NAME 1
#define CONFIG_BT_NIMBLE_SVC_GAP_DEVICE_NAME "nimble"
#define CONFIG_BT_NIMBLE_GAP_DEVICE_NAME_MAX_LEN 31
#define CONFIG_BT_NIMBLE_ATT_PREFERRED_MTU 256
#define CONFIG_BT_NIMBLE_ATT_MAX_PREP_ENTRIES 64
#define CONFIG_BT_NIMBLE_SVC_GAP_APPEARANCE 0x0
#define CONFIG_BT_NIMBLE_MSYS_1_BLOCK_COUNT 12
#define CONFIG_BT_NIMBLE_MSYS_1_BLOCK_SIZE 256
#define CONFIG_BT_NIMBLE_MSYS_2_BLOCK_COUNT 24
#define CONFIG_BT_NIMBLE_MSYS_2_BLOCK_SIZE 320
#define CONFIG_BT_NIMBLE_TRANSPORT_ACL_FROM_LL_COUNT 24
#define CONFIG_BT_NIMBLE_TRANSPORT_ACL_SIZE 255
#define CONFIG_BT_NIMBLE_TRANSPORT_EVT_SIZE 70
#define CONFIG_BT_NIMBLE_TRANSPORT_EVT_COUNT 30
#define CONFIG_BT_NIMBLE_TRANSPORT_EVT_DISCARD_COUNT 8
#define CONFIG_BT_NIMBLE_L2CAP_COC_SDU_BUFF_COUNT 1
#define CONFIG_BT_NIMBLE_GATT_MAX_PROCS 4
#define CONFIG_BT_NIMBLE_RPA_TIMEOUT 900
#define CONFIG_BT_NIMBLE_CRYPTO_STACK_MBEDTLS 1
#define CONFIG_BT_NIMBLE_HS_STOP_TIMEOUT_MS 2000
#define CONFIG_BT_NIMBLE_ENABLE_CONN_REATTEMPT 1
#define CONFIG_BT_NIMBLE_MAX_CONN_REATTEMPT 3
#define CONFIG_BT_NIMBLE_50_FEATURE_SUPPORT 1
#define CONFIG_BT_NIMBLE_LL_CFG_FEAT_LE_2M_PHY 1
#define CONFIG_BT_NIMBLE_LL_CFG_FEAT_LE_CODED_PHY 1
#define CONFIG_BT_NIMBLE_EXT_SCAN 1
#define CONFIG_BT_NIMBLE_ENABLE_PERIODIC_SYNC 1
#define CONFIG_BT_NIMBLE_MAX_PERIODIC_SYNCS 0
#define CONFIG_BT_NIMBLE_WHITELIST_SIZE 12
#define CONFIG_BT_NIMBLE_USE_ESP_TIMER 1
#define CONFIG_BT_NIMBLE_LEGACY_VHCI_ENABLE 1
#define CONFIG_BT_NIMBLE_PROX_SERVICE 1
#define CONFIG_BT_NIMBLE_ANS_SERVICE 1
#define CONFIG_BT_NIMBLE_CTS_SERVICE 1
#define CONFIG_BT_NIMBLE_HTP_SERVICE 1
#define CONFIG_BT_NIMBLE_IPSS_SERVICE 1
#define CONFIG_BT_NIMBLE_TPS_SERVICE 1
#define CONFIG_BT_NIMBLE_IAS_SERVICE 1
#define CONFIG_BT_NIMBLE_LLS_SERVICE 1
#define CONFIG_BT_NIMBLE_SPS_SERVICE 1
#define CONFIG_BT_NIMBLE_HR_SERVICE 1
#define CONFIG_BT_NIMBLE_BAS_SERVICE 1
#define CONFIG_BT_NIMBLE_DIS_SERVICE 1
#define CONFIG_BT_NIMBLE_GAP_SERVICE 1
#define CONFIG_BT_NIMBLE_SVC_GAP_APPEAR_WRITE_PERM 0
#define CONFIG_BT_NIMBLE_SVC_GAP_APPEAR_WRITE_PERM_ENC 0
#define CONFIG_BT_NIMBLE_SVC_GAP_APPEAR_WRITE_PERM_ATHN 0
#define CONFIG_BT_NIMBLE_SVC_GAP_APPEAR_WRITE_PERM_ATHR 0
#define CONFIG_BT_NIMBLE_SVC_GAP_CAR_CHAR_NOT_SUPP 1
#define CONFIG_BT_NIMBLE_SVC_GAP_CENT_ADDR_RESOLUTION -1
#define CONFIG_BT_NIMBLE_SVC_GAP_PPCP_MAX_CONN_INTERVAL 0
#define CONFIG_BT_NIMBLE_SVC_GAP_PPCP_MIN_CONN_INTERVAL 0
#define CONFIG_BT_NIMBLE_SVC_GAP_PPCP_SLAVE_LATENCY 0
#define CONFIG_BT_NIMBLE_SVC_GAP_PPCP_SUPERVISION_TMO 0
#define CONFIG_BT_NIMBLE_SVC_GAP_NAME_WRITE_PERM 0
#define CONFIG_BT_NIMBLE_SVC_GAP_NAME_WRITE_PERM_ENC 0
#define CONFIG_BT_NIMBLE_SVC_GAP_NAME_WRITE_PERM_AUTHEN 0
#define CONFIG_BT_NIMBLE_SVC_GAP_NAME_WRITE_PERM_AUTHOR 0
#define CONFIG_UART_HW_FLOWCTRL_DISABLE 1
#define CONFIG_BT_NIMBLE_HCI_UART_FLOW_CTRL 0
#define CONFIG_BT_NIMBLE_HCI_UART_RTS_PIN 19
#define CONFIG_BT_NIMBLE_HCI_UART_CTS_PIN 23
#define CONFIG_BT_NIMBLE_EATT_CHAN_NUM 0
#define CONFIG_BT_CTRL_MODE_EFF 1
#define CONFIG_BT_CTRL_BLE_MAX_ACT 6
#define CONFIG_BT_CTRL_BLE_MAX_ACT_EFF 6
#define CONFIG_BT_CTRL_BLE_STATIC_ACL_TX_BUF_NB 0
#define CONFIG_BT_CTRL_PINNED_TO_CORE_0 1
#define CONFIG_BT_CTRL_PINNED_TO_CORE 0
#define CONFIG_BT_CTRL_HCI_MODE_VHCI 1
#define CONFIG_BT_CTRL_HCI_TL 1
#define CONFIG_BT_CTRL_ADV_DUP_FILT_MAX 30
#define CONFIG_BT_BLE_CCA_MODE_NONE 1
#define CONFIG_BT_BLE_CCA_MODE 0
#define CONFIG_BT_CTRL_HW_CCA_VAL 20
#define CONFIG_BT_CTRL_HW_CCA_EFF 0
#define CONFIG_BT_CTRL_CE_LENGTH_TYPE_ORIG 1
#define CONFIG_BT_CTRL_CE_LENGTH_TYPE_EFF 0
#define CONFIG_BT_CTRL_TX_ANTENNA_INDEX_0 1
#define CONFIG_BT_CTRL_TX_ANTENNA_INDEX_EFF 0
#define CONFIG_BT_CTRL_RX_ANTENNA_INDEX_0 1
#define CONFIG_BT_CTRL_RX_ANTENNA_INDEX_EFF 0
#define CONFIG_BT_CTRL_DFT_TX_POWER_LEVEL_P9 1
#define CONFIG_BT_CTRL_DFT_TX_POWER_LEVEL_EFF 11
#define CONFIG_BT_CTRL_BLE_ADV_REPORT_FLOW_CTRL_SUPP 1
#define CONFIG_BT_CTRL_BLE_ADV_REPORT_FLOW_CTRL_NUM 100
#define CONFIG_BT_CTRL_BLE_ADV_REPORT_DISCARD_THRSHOLD 20
#define CONFIG_BT_CTRL_BLE_SCAN_DUPL 1
#define CONFIG_BT_CTRL_SCAN_DUPL_TYPE_DEVICE 1
#define CONFIG_BT_CTRL_SCAN_DUPL_TYPE 0
#define CONFIG_BT_CTRL_SCAN_DUPL_CACHE_SIZE 100
#define CONFIG_BT_CTRL_DUPL_SCAN_CACHE_REFRESH_PERIOD 0
#define CONFIG_BT_CTRL_COEX_PHY_CODED_TX_RX_TLIM_DIS 1
#define CONFIG_BT_CTRL_COEX_PHY_CODED_TX_RX_TLIM_EFF 0
#define CONFIG_BT_CTRL_SLEEP_MODE_EFF 0
#define CONFIG_BT_CTRL_SLEEP_CLOCK_EFF 0
#define CONFIG_BT_CTRL_HCI_TL_EFF 1
#define CONFIG_BT_CTRL_CHAN_ASS_EN 1
#define CONFIG_BT_CTRL_LE_PING_EN 1
#define CONFIG_BT_CTRL_DTM_ENABLE 1
#define CONFIG_BT_CTRL_BLE_MASTER 1
#define CONFIG_BT_CTRL_BLE_SCAN 1
#define CONFIG_BT_CTRL_BLE_SECURITY_ENABLE 1
#define CONFIG_BT_CTRL_BLE_ADV 1
#define CONFIG_BT_ALARM_MAX_NUM 50
#define CONFIG_TWAI_ERRATA_FIX_LISTEN_ONLY_DOM 1
#define CONFIG_EFUSE_MAX_BLK_LEN 256
#define CONFIG_ESP_TLS_USING_MBEDTLS 1
#define CONFIG_ESP_TLS_USE_DS_PERIPHERAL 1
#define CONFIG_ESP_COEX_ENABLED 1
#define CONFIG_ESP_COEX_SW_COEXIST_ENABLE 1
#define CONFIG_ESP_ERR_TO_NAME_LOOKUP 1
#define CONFIG_GPTIMER_ISR_HANDLER_IN_IRAM 1
#define CONFIG_GPTIMER_OBJ_CACHE_SAFE 1
#define CONFIG_SPI_MASTER_ISR_IN_IRAM 1
#define CONFIG_SPI_SLAVE_ISR_IN_IRAM 1
#define CONFIG_USJ_ENABLE_USB_SERIAL_JTAG 1
#define CONFIG_ETH_ENABLED 1
#define CONFIG_ETH_USE_SPI_ETHERNET 1
#define CONFIG_ESP_EVENT_POST_FROM_ISR 1
#define CONFIG_ESP_EVENT_POST_FROM_IRAM_ISR 1
#define CONFIG_ESP_GDBSTUB_ENABLED 1
#define CONFIG_ESP_GDBSTUB_SUPPORT_TASKS 1
#define CONFIG_ESP_GDBSTUB_MAX_TASKS 32
#define CONFIG_ESPHID_TASK_SIZE_BT 2048
#define CONFIG_ESPHID_TASK_SIZE_BLE 4096
#define CONFIG_ESP_HTTP_CLIENT_ENABLE_HTTPS 1
#define CONFIG_ESP_HTTP_CLIENT_EVENT_POST_TIMEOUT 2000
#define CONFIG_HTTPD_MAX_REQ_HDR_LEN 512
#define CONFIG_HTTPD_MAX_URI_LEN 512
#define CONFIG_HTTPD_ERR_RESP_NO_DELAY 1
#define CONFIG_HTTPD_PURGE_BUF_LEN 32
#define CONFIG_HTTPD_SERVER_EVENT_POST_TIMEOUT 2000
#define CONFIG_ESP_HTTPS_OTA_EVENT_POST_TIMEOUT 2000
#define CONFIG_ESP_HTTPS_SERVER_EVENT_POST_TIMEOUT 2000
#define CONFIG_ESP32S3_REV_MIN_0 1
#define CONFIG_ESP32S3_REV_MIN_FULL 0
#define CONFIG_ESP_REV_MIN_FULL 0
#define CONFIG_ESP32S3_REV_MAX_FULL 99
#define CONFIG_ESP_REV_MAX_FULL 99
#define CONFIG_ESP_EFUSE_BLOCK_REV_MIN_FULL 0
#define CONFIG_ESP_EFUSE_BLOCK_REV_MAX_FULL 199
#define CONFIG_ESP_MAC_ADDR_UNIVERSE_WIFI_STA 1
#define CONFIG_ESP_MAC_ADDR_UNIVERSE_WIFI_AP 1
#define CONFIG_ESP_MAC_ADDR_UNIVERSE_BT 1
#define CONFIG_ESP_MAC_ADDR_UNIVERSE_ETH 1
#define CONFIG_ESP_MAC_UNIVERSAL_MAC_ADDRESSES_FOUR 1
#define CONFIG_ESP_MAC_UNIVERSAL_MAC_ADDRESSES 4
#define CONFIG_ESP32S3_UNIVERSAL_MAC_ADDRESSES_FOUR 1
#define CONFIG_ESP32S3_UNIVERSAL_MAC_ADDRESSES 4
#define CONFIG_ESP_SLEEP_FLASH_LEAKAGE_WORKAROUND 1
#define CONFIG_ESP_SLEEP_MSPI_NEED_ALL_IO_PU 1
#define CONFIG_ESP_SLEEP_RTC_BUS_ISO_WORKAROUND 1
#define CONFIG_ESP_SLEEP_GPIO_RESET_WORKAROUND 1
#define CONFIG_ESP_SLEEP_WAIT_FLASH_READY_EXTRA_DELAY 2000
#define CONFIG_ESP_SLEEP_GPIO_ENABLE_INTERNAL_RESISTORS 1
#define CONFIG_RTC_CLK_SRC_INT_RC 1
#define CONFIG_RTC_CLK_CAL_CYCLES 1024
#define CONFIG_GDMA_CTRL_FUNC_IN_IRAM 1
#define CONFIG_GDMA_ISR_HANDLER_IN_IRAM 1
#define CONFIG_GDMA_OBJ_DRAM_SAFE 1
#define CONFIG_XTAL_FREQ_40 1
#define CONFIG_XTAL_FREQ 40
#define CONFIG_ESP_SPI_BUS_LOCK_ISR_FUNCS_IN_IRAM 1
#define CONFIG_ESP_NETIF_IP_LOST_TIMER_INTERVAL 120
#define CONFIG_ESP_NETIF_TCPIP_LWIP 1
#define CONFIG_ESP_NETIF_USES_TCPIP_WITH_BSD_API 1
#define CONFIG_ESP_NETIF_REPORT_DATA_TRAFFIC 1
#define CONFIG_ESP_PHY_ENABLED 1
#define CONFIG_ESP_PHY_CALIBRATION_AND_DATA_STORAGE 1
#define CONFIG_ESP_PHY_MAX_WIFI_TX_POWER 20
#define CONFIG_ESP_PHY_MAX_TX_POWER 20
#define CONFIG_ESP_PHY_ENABLE_USB 1
#define CONFIG_ESP_PHY_RF_CAL_PARTIAL 1
#define CONFIG_ESP_PHY_CALIBRATION_MODE 0
#define CONFIG_PM_POWER_DOWN_CPU_IN_LIGHT_SLEEP 1
#define CONFIG_PM_RESTORE_CACHE_TAGMEM_AFTER_LIGHT_SLEEP 1
#define CONFIG_ESP_DEFAULT_CPU_FREQ_MHZ_160 1
#define CONFIG_ESP_DEFAULT_CPU_FREQ_MHZ 160
#define CONFIG_ESP32S3_INSTRUCTION_CACHE_16KB 1
#define CONFIG_ESP32S3_INSTRUCTION_CACHE_SIZE 0x4000
#define CONFIG_ESP32S3_INSTRUCTION_CACHE_8WAYS 1
#define CONFIG_ESP32S3_ICACHE_ASSOCIATED_WAYS 8
#define CONFIG_ESP32S3_INSTRUCTION_CACHE_LINE_32B 1
#define CONFIG_ESP32S3_INSTRUCTION_CACHE_LINE_SIZE 32
#define CONFIG_ESP32S3_DATA_CACHE_32KB 1
#define CONFIG_ESP32S3_DATA_CACHE_SIZE 0x8000
#define CONFIG_ESP32S3_DATA_CACHE_8WAYS 1
#define CONFIG_ESP32S3_DCACHE_ASSOCIATED_WAYS 8
#define CONFIG_ESP32S3_DATA_CACHE_LINE_32B 1
#define CONFIG_ESP32S3_DATA_CACHE_LINE_SIZE 32
#define CONFIG_ESP32S3_TRACEMEM_RESERVE_DRAM 0x0
#define CONFIG_ESP_SYSTEM_PANIC_PRINT_REBOOT 1
#define CONFIG_ESP_SYSTEM_PANIC_REBOOT_DELAY_SECONDS 0
#define CONFIG_ESP_SYSTEM_RTC_FAST_MEM_AS_HEAP_DEPCHECK 1
#define CONFIG_ESP_SYSTEM_ALLOW_RTC_FAST_MEM_AS_HEAP 1
#define CONFIG_ESP_SYSTEM_MEMPROT_FEATURE 1
#define CONFIG_ESP_SYSTEM_MEMPROT_FEATURE_LOCK 1
#define CONFIG_ESP_SYSTEM_EVENT_QUEUE_SIZE 32
#define CONFIG_ESP_SYSTEM_EVENT_TASK_STACK_SIZE 2304
#define CONFIG_ESP_MAIN_TASK_STACK_SIZE 3584
#define CONFIG_ESP_MAIN_TASK_AFFINITY_CPU0 1
#define CONFIG_ESP_MAIN_TASK_AFFINITY 0x0
#define CONFIG_ESP_MINIMAL_SHARED_STACK_SIZE 2048
#define CONFIG_ESP_CONSOLE_UART_DEFAULT 1
#define CONFIG_ESP_CONSOLE_SECONDARY_USB_SERIAL_JTAG 1
#define CONFIG_ESP_CONSOLE_USB_SERIAL_JTAG_ENABLED 1
#define CONFIG_ESP_CONSOLE_UART 1
#define CONFIG_ESP_CONSOLE_UART_NUM 0
#define CONFIG_ESP_CONSOLE_ROM_SERIAL_PORT_NUM 0
#define CONFIG_ESP_CONSOLE_UART_BAUDRATE 115200
#define CONFIG_ESP_INT_WDT 1
#define CONFIG_ESP_INT_WDT_TIMEOUT_MS 300
#define CONFIG_ESP_INT_WDT_CHECK_CPU1 1
#define CONFIG_ESP_TASK_WDT_EN 1
#define CONFIG_ESP_TASK_WDT_INIT 1
#define CONFIG_ESP_TASK_WDT_TIMEOUT_S 5
#define CONFIG_ESP_TASK_WDT_CHECK_IDLE_TASK_CPU0 1
#define CONFIG_ESP_TASK_WDT_CHECK_IDLE_TASK_CPU1 1
#define CONFIG_ESP_DEBUG_OCDAWARE 1
#define CONFIG_ESP_SYSTEM_CHECK_INT_LEVEL_4 1
#define CONFIG_ESP_BROWNOUT_DET 1
#define CONFIG_ESP_BROWNOUT_DET_LVL_SEL_7 1
#define CONFIG_ESP_BROWNOUT_DET_LVL 7
#define CONFIG_ESP_SYSTEM_BROWNOUT_INTR 1
#define CONFIG_ESP_SYSTEM_BBPLL_RECALIB 1
#define CONFIG_ESP_IPC_TASK_STACK_SIZE 1280
#define CONFIG_ESP_IPC_USES_CALLERS_PRIORITY 1
#define CONFIG_ESP_IPC_ISR_ENABLE 1
#define CONFIG_ESP_TIME_FUNCS_USE_RTC_TIMER 1
#define CONFIG_ESP_TIME_FUNCS_USE_ESP_TIMER 1
#define CONFIG_ESP_TIMER_TASK_STACK_SIZE 3584
#define CONFIG_ESP_TIMER_INTERRUPT_LEVEL 1
#define CONFIG_ESP_TIMER_TASK_AFFINITY 0x0
#define CONFIG_ESP_TIMER_TASK_AFFINITY_CPU0 1
#define CONFIG_ESP_TIMER_ISR_AFFINITY_CPU0 1
#define CONFIG_ESP_TIMER_IMPL_SYSTIMER 1
#define CONFIG_ESP_WIFI_ENABLED 1
#define CONFIG_ESP_WIFI_STATIC_RX_BUFFER_NUM 10
#define CONFIG_ESP_WIFI_DYNAMIC_RX_BUFFER_NUM 32
#define CONFIG_ESP_WIFI_DYNAMIC_TX_BUFFER 1
#define CONFIG_ESP_WIFI_TX_BUFFER_TYPE 1
#define CONFIG_ESP_WIFI_DYNAMIC_TX_BUFFER_NUM 32
#define CONFIG_ESP_WIFI_STATIC_RX_MGMT_BUFFER 1
#define CONFIG_ESP_WIFI_DYNAMIC_RX_MGMT_BUF 0
#define CONFIG_ESP_WIFI_RX_MGMT_BUF_NUM_DEF 5
#define CONFIG_ESP_WIFI_AMPDU_TX_ENABLED 1
#define CONFIG_ESP_WIFI_TX_BA_WIN 6
#define CONFIG_ESP_WIFI_AMPDU_RX_ENABLED 1
#define CONFIG_ESP_WIFI_RX_BA_WIN 6
#define CONFIG_ESP_WIFI_NVS_ENABLED 1
#define CONFIG_ESP_WIFI_TASK_PINNED_TO_CORE_0 1
#define CONFIG_ESP_WIFI_SOFTAP_BEACON_MAX_LEN 752
#define CONFIG_ESP_WIFI_MGMT_SBUF_NUM 32
#define CONFIG_ESP_WIFI_IRAM_OPT 1
#define CONFIG_ESP_WIFI_RX_IRAM_OPT 1
#define CONFIG_ESP_WIFI_ENABLE_WPA3_SAE 1
#define CONFIG_ESP_WIFI_ENABLE_SAE_PK 1
#define CONFIG_ESP_WIFI_SOFTAP_SAE_SUPPORT 1
#define CONFIG_ESP_WIFI_ENABLE_WPA3_OWE_STA 1
#define CONFIG_ESP_WIFI_SLP_DEFAULT_MIN_ACTIVE_TIME 50
#define CONFIG_ESP_WIFI_SLP_DEFAULT_MAX_ACTIVE_TIME 10
#define CONFIG_ESP_WIFI_SLP_DEFAULT_WAIT_BROADCAST_DATA_TIME 15
#define CONFIG_ESP_WIFI_STA_DISCONNECTED_PM_ENABLE 1
#define CONFIG_ESP_WIFI_GMAC_SUPPORT 1
#define CONFIG_ESP_WIFI_SOFTAP_SUPPORT 1
#define CONFIG_ESP_WIFI_ESPNOW_MAX_ENCRYPT_NUM 7
#define CONFIG_ESP_WIFI_MBEDTLS_CRYPTO 1
#define CONFIG_ESP_WIFI_MBEDTLS_TLS_CLIENT 1
#define CONFIG_ESP_WIFI_ENTERPRISE_SUPPORT 1
#define CONFIG_ESP_COREDUMP_ENABLE_TO_NONE 1
#define CONFIG_FATFS_VOLUME_COUNT 2
#define CONFIG_FATFS_LFN_NONE 1
#define CONFIG_FATFS_SECTOR_4096 1
#define CONFIG_FATFS_CODEPAGE_437 1
#define CONFIG_FATFS_CODEPAGE 437
#define CONFIG_FATFS_FS_LOCK 0
#define CONFIG_FATFS_TIMEOUT_MS 10000
#define CONFIG_FATFS_PER_FILE_CACHE 1
#define CONFIG_FATFS_USE_STRFUNC_NONE 1
#define CONFIG_FATFS_VFS_FSTAT_BLKSIZE 0
#define CONFIG_FATFS_LINK_LOCK 1
#define CONFIG_FATFS_DONT_TRUST_FREE_CLUSTER_CNT 0
#define CONFIG_FATFS_DONT_TRUST_LAST_ALLOC 0
#define CONFIG_FREERTOS_HZ 100
#define CONFIG_FREERTOS_CHECK_STACKOVERFLOW_CANARY 1
#define CONFIG_FREERTOS_THREAD_LOCAL_STORAGE_POINTERS 1
#define CONFIG_FREERTOS_IDLE_TASK_STACKSIZE 1536
#define CONFIG_FREERTOS_MAX_TASK_NAME_LEN 16
#define CONFIG_FREERTOS_USE_TIMERS 1
#define CONFIG_FREERTOS_TIMER_SERVICE_TASK_NAME "Tmr Svc"
#define CONFIG_FREERTOS_TIMER_TASK_NO_AFFINITY 1
#define CONFIG_FREERTOS_TIMER_SERVICE_TASK_CORE_AFFINITY 0x7FFFFFFF
#define CONFIG_FREERTOS_TIMER_TASK_PRIORITY 1
#define CONFIG_FREERTOS_TIMER_TASK_STACK_DEPTH 2048
#define CONFIG_FREERTOS_TIMER_QUEUE_LENGTH 10
#define CONFIG_FREERTOS_QUEUE_REGISTRY_SIZE 0
#define CONFIG_FREERTOS_TASK_NOTIFICATION_ARRAY_ENTRIES 1
#define CONFIG_FREERTOS_TASK_FUNCTION_WRAPPER 1
#define CONFIG_FREERTOS_TLSP_DELETION_CALLBACKS 1
#define CONFIG_FREERTOS_CHECK_MUTEX_GIVEN_BY_OWNER 1
#define CONFIG_FREERTOS_ISR_STACKSIZE 1536
#define CONFIG_FREERTOS_INTERRUPT_BACKTRACE 1
#define CONFIG_FREERTOS_TICK_SUPPORT_SYSTIMER 1
#define CONFIG_FREERTOS_CORETIMER_SYSTIMER_LVL1 1
#define CONFIG_FREERTOS_SYSTICK_USES_SYSTIMER 1
#define CONFIG_FREERTOS_PORT 1
#define CONFIG_FREERTOS_NO_AFFINITY 0x7FFFFFFF
#define CONFIG_FREERTOS_SUPPORT_STATIC_ALLOCATION 1
#define CONFIG_FREERTOS_DEBUG_OCDAWARE 1
#define CONFIG_FREERTOS_ENABLE_TASK_SNAPSHOT 1
#define CONFIG_FREERTOS_PLACE_SNAPSHOT_FUNS_INTO_FLASH 1
#define CONFIG_FREERTOS_NUMBER_OF_CORES 2
#define CONFIG_HAL_ASSERTION_EQUALS_SYSTEM 1
#define CONFIG_HAL_DEFAULT_ASSERTION_LEVEL 2
#define CONFIG_HAL_WDT_USE_ROM_IMPL 1
#define CONFIG_HAL_SPI_MASTER_FUNC_IN_IRAM 1
#define CONFIG_HAL_SPI_SLAVE_FUNC_IN_IRAM 1
#define CONFIG_HEAP_POISONING_DISABLED 1
#define CONFIG_HEAP_TRACING_OFF 1
#define CONFIG_LOG_DEFAULT_LEVEL_INFO 1
#define CONFIG_LOG_DEFAULT_LEVEL 3
#define CONFIG_LOG_MAXIMUM_EQUALS_DEFAULT 1
#define CONFIG_LOG_MAXIMUM_LEVEL 3
#define CONFIG_LOG_DYNAMIC_LEVEL_CONTROL 1
#define CONFIG_LOG_TAG_LEVEL_IMPL_CACHE_AND_LINKED_LIST 1
#define CONFIG_LOG_TAG_LEVEL_CACHE_BINARY_MIN_HEAP 1
#define CONFIG_LOG_TAG_LEVEL_IMPL_CACHE_SIZE 31
#define CONFIG_LOG_TIMESTAMP_SOURCE_RTOS 1
#define CONFIG_LWIP_ENABLE 1
#define CONFIG_LWIP_LOCAL_HOSTNAME "espressif"
#define CONFIG_LWIP_TCPIP_TASK_PRIO 18
#define CONFIG_LWIP_DNS_SUPPORT_MDNS_QUERIES 1
#define CONFIG_LWIP_TIMERS_ONDEMAND 1
#define CONFIG_LWIP_ND6 1
#define CONFIG_LWIP_MAX_SOCKETS 10
#define CONFIG_LWIP_SO_REUSE 1
#define CONFIG_LWIP_SO_REUSE_RXTOALL 1
#define CONFIG_LWIP_IP_DEFAULT_TTL 64
#define CONFIG_LWIP_IP4_FRAG 1
#define CONFIG_LWIP_IP6_FRAG 1
#define CONFIG_LWIP_IP_REASS_MAX_PBUFS 10
#define CONFIG_LWIP_ESP_GRATUITOUS_ARP 1
#define CONFIG_LWIP_GARP_TMR_INTERVAL 60
#define CONFIG_LWIP_ESP_MLDV6_REPORT 1
#define CONFIG_LWIP_MLDV6_TMR_INTERVAL 40
#define CONFIG_LWIP_TCPIP_RECVMBOX_SIZE 32
#define CONFIG_LWIP_DHCP_DOES_ARP_CHECK 1
#define CONFIG_LWIP_DHCP_DISABLE_VENDOR_CLASS_ID 1
#define CONFIG_LWIP_DHCP_OPTIONS_LEN 68
#define CONFIG_LWIP_NUM_NETIF_CLIENT_DATA 0
#define CONFIG_LWIP_DHCP_COARSE_TIMER_SECS 1
#define CONFIG_LWIP_DHCPS 1
#define CONFIG_LWIP_DHCPS_LEASE_UNIT 60
#define CONFIG_LWIP_DHCPS_MAX_STATION_NUM 8
#define CONFIG_LWIP_DHCPS_STATIC_ENTRIES 1
#define CONFIG_LWIP_DHCPS_ADD_DNS 1
#define CONFIG_LWIP_IPV4 1
#define CONFIG_LWIP_IPV6 1
#define CONFIG_LWIP_IPV6_NUM_ADDRESSES 3
#define CONFIG_LWIP_NETIF_LOOPBACK 1
#define CONFIG_LWIP_LOOPBACK_MAX_PBUFS 8
#define CONFIG_LWIP_MAX_ACTIVE_TCP 16
#define CONFIG_LWIP_MAX_LISTENING_TCP 16
#define CONFIG_LWIP_TCP_HIGH_SPEED_RETRANSMISSION 1
#define CONFIG_LWIP_TCP_MAXRTX 12
#define CONFIG_LWIP_TCP_SYNMAXRTX 12
#define CONFIG_LWIP_TCP_MSS 1440
#define CONFIG_LWIP_TCP_TMR_INTERVAL 250
#define CONFIG_LWIP_TCP_MSL 60000
#define CONFIG_LWIP_TCP_FIN_WAIT_TIMEOUT 20000
#define CONFIG_LWIP_TCP_SND_BUF_DEFAULT 5760
#define CONFIG_LWIP_TCP_WND_DEFAULT 5760
#define CONFIG_LWIP_TCP_RECVMBOX_SIZE 6
#define CONFIG_LWIP_TCP_ACCEPTMBOX_SIZE 6
#define CONFIG_LWIP_TCP_QUEUE_OOSEQ 1
#define CONFIG_LWIP_TCP_OOSEQ_TIMEOUT 6
#define CONFIG_LWIP_TCP_OOSEQ_MAX_PBUFS 4
#define CONFIG_LWIP_TCP_OVERSIZE_MSS 1
#define CONFIG_LWIP_TCP_RTO_TIME 1500
#define CONFIG_LWIP_MAX_UDP_PCBS 16
#define CONFIG_LWIP_UDP_RECVMBOX_SIZE 6
#define CONFIG_LWIP_CHECKSUM_CHECK_ICMP 1
#define CONFIG_LWIP_TCPIP_TASK_STACK_SIZE 3072
#define CONFIG_LWIP_TCPIP_TASK_AFFINITY_NO_AFFINITY 1
#define CONFIG_LWIP_TCPIP_TASK_AFFINITY 0x7FFFFFFF
#define CONFIG_LWIP_IPV6_MEMP_NUM_ND6_QUEUE 3
#define CONFIG_LWIP_IPV6_ND6_NUM_NEIGHBORS 5
#define CONFIG_LWIP_IPV6_ND6_NUM_PREFIXES 5
#define CONFIG_LWIP_IPV6_ND6_NUM_ROUTERS 3
#define CONFIG_LWIP_IPV6_ND6_NUM_DESTINATIONS 10
#define CONFIG_LWIP_ICMP 1
#define CONFIG_LWIP_MAX_RAW_PCBS 16
#define CONFIG_LWIP_SNTP_MAX_SERVERS 1
#define CONFIG_LWIP_SNTP_UPDATE_DELAY 3600000
#define CONFIG_LWIP_SNTP_STARTUP_DELAY 1
#define CONFIG_LWIP_SNTP_MAXIMUM_STARTUP_DELAY 5000
#define CONFIG_LWIP_DNS_MAX_HOST_IP 1
#define CONFIG_LWIP_DNS_MAX_SERVERS 3
#define CONFIG_LWIP_BRIDGEIF_MAX_PORTS 7
#define CONFIG_LWIP_ESP_LWIP_ASSERT 1
#define CONFIG_LWIP_HOOK_TCP_ISN_DEFAULT 1
#define CONFIG_LWIP_HOOK_IP6_ROUTE_NONE 1
#define CONFIG_LWIP_HOOK_ND6_GET_GW_NONE 1
#define CONFIG_LWIP_HOOK_IP6_SELECT_SRC_ADDR_NONE 1
#define CONFIG_LWIP_HOOK_NETCONN_EXT_RESOLVE_NONE 1
#define CONFIG_LWIP_HOOK_DNS_EXT_RESOLVE_NONE 1
#define CONFIG_LWIP_HOOK_IP6_INPUT_DEFAULT 1
#define CONFIG_MBEDTLS_INTERNAL_MEM_ALLOC 1
#define CONFIG_MBEDTLS_ASYMMETRIC_CONTENT_LEN 1
#define CONFIG_MBEDTLS_SSL_IN_CONTENT_LEN 16384
#define CONFIG_MBEDTLS_SSL_OUT_CONTENT_LEN 4096
#define CONFIG_MBEDTLS_SSL_KEEP_PEER_CERTIFICATE 1
#define CONFIG_MBEDTLS_PKCS7_C 1
#define CONFIG_MBEDTLS_CERTIFICATE_BUNDLE 1
#define CONFIG_MBEDTLS_CERTIFICATE_BUNDLE_DEFAULT_FULL 1
#define CONFIG_MBEDTLS_CERTIFICATE_BUNDLE_MAX_CERTS 200
#define CONFIG_MBEDTLS_CMAC_C 1
#define CONFIG_MBEDTLS_HARDWARE_AES 1
#define CONFIG_MBEDTLS_AES_USE_INTERRUPT 1
#define CONFIG_MBEDTLS_AES_INTERRUPT_LEVEL 0
#define CONFIG_MBEDTLS_GCM_SUPPORT_NON_AES_CIPHER 1
#define CONFIG_MBEDTLS_HARDWARE_MPI 1
#define CONFIG_MBEDTLS_MPI_USE_INTERRUPT 1
#define CONFIG_MBEDTLS_MPI_INTERRUPT_LEVEL 0
#define CONFIG_MBEDTLS_HARDWARE_SHA 1
#define CONFIG_MBEDTLS_ROM_MD5 1
#define CONFIG_MBEDTLS_HAVE_TIME 1
#define CONFIG_MBEDTLS_ECDSA_DETERMINISTIC 1
#define CONFIG_MBEDTLS_SHA1_C 1
#define CONFIG_MBEDTLS_SHA512_C 1
#define CONFIG_MBEDTLS_TLS_SERVER_AND_CLIENT 1
#define CONFIG_MBEDTLS_TLS_SERVER 1
#define CONFIG_MBEDTLS_TLS_CLIENT 1
#define CONFIG_MBEDTLS_TLS_ENABLED 1
#define CONFIG_MBEDTLS_KEY_EXCHANGE_RSA 1
#define CONFIG_MBEDTLS_KEY_EXCHANGE_ELLIPTIC_CURVE 1
#define CONFIG_MBEDTLS_KEY_EXCHANGE_ECDHE_RSA 1
#define CONFIG_MBEDTLS_KEY_EXCHANGE_ECDHE_ECDSA 1
#define CONFIG_MBEDTLS_KEY_EXCHANGE_ECDH_ECDSA 1
#define CONFIG_MBEDTLS_KEY_EXCHANGE_ECDH_RSA 1
#define CONFIG_MBEDTLS_SSL_RENEGOTIATION 1
#define CONFIG_MBEDTLS_SSL_PROTO_TLS1_2 1
#define CONFIG_MBEDTLS_SSL_ALPN 1
#define CONFIG_MBEDTLS_CLIENT_SSL_SESSION_TICKETS 1
#define CONFIG_MBEDTLS_SERVER_SSL_SESSION_TICKETS 1
#define CONFIG_MBEDTLS_AES_C 1
#define CONFIG_MBEDTLS_CCM_C 1
#define CONFIG_MBEDTLS_GCM_C 1
#define CONFIG_MBEDTLS_PEM_PARSE_C 1
#define CONFIG_MBEDTLS_PEM_WRITE_C 1
#define CONFIG_MBEDTLS_X509_CRL_PARSE_C 1
#define CONFIG_MBEDTLS_X509_CSR_PARSE_C 1
#define CONFIG_MBEDTLS_ECP_C 1
#define CONFIG_MBEDTLS_PK_PARSE_EC_EXTENDED 1
#define CONFIG_MBEDTLS_PK_PARSE_EC_COMPRESSED 1
#define CONFIG_MBEDTLS_ECDH_C 1
#define CONFIG_MBEDTLS_ECDSA_C 1
#define CONFIG_MBEDTLS_ECP_DP_SECP192R1_ENABLED 1
#define CONFIG_MBEDTLS_ECP_DP_SECP224R1_ENABLED 1
#define CONFIG_MBEDTLS_ECP_DP_SECP256R1_ENABLED 1
#define CONFIG_MBEDTLS_ECP_DP_SECP384R1_ENABLED 1
#define CONFIG_MBEDTLS_ECP_DP_SECP521R1_ENABLED 1
#define CONFIG_MBEDTLS_ECP_DP_SECP192K1_ENABLED 1
#define CONFIG_MBEDTLS_ECP_DP_SECP224K1_ENABLED 1
#define CONFIG_MBEDTLS_ECP_DP_SECP256K1_ENABLED 1
#define CONFIG_MBEDTLS_ECP_DP_BP256R1_ENABLED 1
#define CONFIG_MBEDTLS_ECP_DP_BP384R1_ENABLED 1
#define CONFIG_MBEDTLS_ECP_DP_BP512R1_ENABLED 1
#define CONFIG_MBEDTLS_ECP_DP_CURVE25519_ENABLED 1
#define CONFIG_MBEDTLS_ECP_NIST_OPTIM 1
#define CONFIG_MBEDTLS_ERROR_STRINGS 1
#define CONFIG_MBEDTLS_FS_IO 1
#define CONFIG_MQTT_PROTOCOL_311 1
#define CONFIG_MQTT_TRANSPORT_SSL 1
#define CONFIG_MQTT_TRANSPORT_WEBSOCKET 1
#define CONFIG_MQTT_TRANSPORT_WEBSOCKET_SECURE 1
#define CONFIG_NEWLIB_STDOUT_LINE_ENDING_CRLF 1
#define CONFIG_NEWLIB_STDIN_LINE_ENDING_CR 1
#define CONFIG_NEWLIB_TIME_SYSCALL_USE_RTC_HRT 1
#define CONFIG_ESP_PROTOCOMM_SUPPORT_SECURITY_VERSION_0 1
#define CONFIG_ESP_PROTOCOMM_SUPPORT_SECURITY_VERSION_1 1
#define CONFIG_ESP_PROTOCOMM_SUPPORT_SECURITY_VERSION_2 1
#define CONFIG_ESP_PROTOCOMM_SUPPORT_SECURITY_PATCH_VERSION 1
#define CONFIG_PTHREAD_TASK_PRIO_DEFAULT 5
#define CONFIG_PTHREAD_TASK_STACK_SIZE_DEFAULT 3072
#define CONFIG_PTHREAD_STACK_MIN 768
#define CONFIG_PTHREAD_DEFAULT_CORE_NO_AFFINITY 1
#define CONFIG_PTHREAD_TASK_CORE_DEFAULT -1
#define CONFIG_PTHREAD_TASK_NAME_DEFAULT "pthread"
#define CONFIG_MMU_PAGE_SIZE_64KB 1
#define CONFIG_MMU_PAGE_MODE "64KB"
#define CONFIG_MMU_PAGE_SIZE 0x10000
#define CONFIG_SPI_FLASH_BROWNOUT_RESET_XMC 1
#define CONFIG_SPI_FLASH_BROWNOUT_RESET 1
#define CONFIG_SPI_FLASH_HPM_AUTO 1
#define CONFIG_SPI_FLASH_HPM_ON 1
#define CONFIG_SPI_FLASH_HPM_DC_AUTO 1
#define CONFIG_SPI_FLASH_SUSPEND_TSUS_VAL_US 50
#define CONFIG_SPI_FLASH_ROM_DRIVER_PATCH 1
#define CONFIG_SPI_FLASH_DANGEROUS_WRITE_ABORTS 1
#define CONFIG_SPI_FLASH_YIELD_DURING_ERASE 1
#define CONFIG_SPI_FLASH_ERASE_YIELD_DURATION_MS 20
#define CONFIG_SPI_FLASH_ERASE_YIELD_TICKS 1
#define CONFIG_SPI_FLASH_WRITE_CHUNK_SIZE 8192
#define CONFIG_SPI_FLASH_VENDOR_XMC_SUPPORTED 1
#define CONFIG_SPI_FLASH_VENDOR_GD_SUPPORTED 1
#define CONFIG_SPI_FLASH_VENDOR_ISSI_SUPPORTED 1
#define CONFIG_SPI_FLASH_VENDOR_MXIC_SUPPORTED 1
#define CONFIG_SPI_FLASH_VENDOR_WINBOND_SUPPORTED 1
#define CONFIG_SPI_FLASH_VENDOR_BOYA_SUPPORTED 1
#define CONFIG_SPI_FLASH_VENDOR_TH_SUPPORTED 1
#define CONFIG_SPI_FLASH_SUPPORT_ISSI_CHIP 1
#define CONFIG_SPI_FLASH_SUPPORT_MXIC_CHIP 1
#define CONFIG_SPI_FLASH_SUPPORT_GD_CHIP 1
#define CONFIG_SPI_FLASH_SUPPORT_WINBOND_CHIP 1
#define CONFIG_SPI_FLASH_SUPPORT_BOYA_CHIP 1
#define CONFIG_SPI_FLASH_SUPPORT_TH_CHIP 1
#define CONFIG_SPI_FLASH_SUPPORT_MXIC_OPI_CHIP 1
#define CONFIG_SPI_FLASH_ENABLE_ENCRYPTED_READ_WRITE 1
#define CONFIG_SPIFFS_MAX_PARTITIONS 3
#define CONFIG_SPIFFS_CACHE 1
#define CONFIG_SPIFFS_CACHE_WR 1
#define CONFIG_SPIFFS_PAGE_CHECK 1
#define CONFIG_SPIFFS_GC_MAX_RUNS 10
#define CONFIG_SPIFFS_PAGE_SIZE 256
#define CONFIG_SPIFFS_OBJ_NAME_LEN 32
#define CONFIG_SPIFFS_USE_MAGIC 1
#define CONFIG_SPIFFS_USE_MAGIC_LENGTH 1
#define CONFIG_SPIFFS_META_LENGTH 4
#define CONFIG_SPIFFS_USE_MTIME 1
#define CONFIG_WS_TRANSPORT 1
#define CONFIG_WS_BUFFER_SIZE 1024
#define CONFIG_UNITY_ENABLE_FLOAT 1
#define CONFIG_UNITY_ENABLE_DOUBLE 1
#define CONFIG_UNITY_ENABLE_IDF_TEST_RUNNER 1
#define CONFIG_USB_HOST_CONTROL_TRANSFER_MAX_SIZE 256
#define CONFIG_USB_HOST_HW_BUFFER_BIAS_BALANCED 1
#define CONFIG_USB_HOST_DEBOUNCE_DELAY_MS 250
#define CONFIG_USB_HOST_RESET_HOLD_MS 30
#define CONFIG_USB_HOST_RESET_RECOVERY_MS 30
#define CONFIG_USB_HOST_SET_ADDR_RECOVERY_MS 10
#define CONFIG_USB_OTG_SUPPORTED 1
#define CONFIG_VFS_SUPPORT_IO 1
#define CONFIG_VFS_SUPPORT_DIR 1
#define CONFIG_VFS_SUPPORT_SELECT 1
#define CONFIG_VFS_SUPPRESS_SELECT_DEBUG_OUTPUT 1
#define CONFIG_VFS_SUPPORT_TERMIOS 1
#define CONFIG_VFS_MAX_COUNT 8
#define CONFIG_VFS_SEMIHOSTFS_MAX_MOUNT_POINTS 1
#define CONFIG_VFS_INITIALIZE_DEV_NULL 1
#define CONFIG_WL_SECTOR_SIZE_4096 1
#define CONFIG_WL_SECTOR_SIZE 4096
#define CONFIG_WIFI_PROV_SCAN_MAX_ENTRIES 16
#define CONFIG_WIFI_PROV_AUTOSTOP_TIMEOUT 30
#define CONFIG_WIFI_PROV_BLE_SEC_CONN 1
#define CONFIG_WIFI_PROV_STA_ALL_CHANNEL_SCAN 1

/* List of deprecated options */
#define CONFIG_BROWNOUT_DET CONFIG_ESP_BROWNOUT_DET
#define CONFIG_BROWNOUT_DET_LVL CONFIG_ESP_BROWNOUT_DET_LVL
#define CONFIG_BROWNOUT_DET_LVL_SEL_7 CONFIG_ESP_BROWNOUT_DET_LVL_SEL_7
#define CONFIG_BT_NIMBLE_ACL_BUF_COUNT CONFIG_BT_NIMBLE_TRANSPORT_ACL_FROM_LL_COUNT
#define CONFIG_BT_NIMBLE_ACL_BUF_SIZE CONFIG_BT_NIMBLE_TRANSPORT_ACL_SIZE
#define CONFIG_BT_NIMBLE_COEX_PHY_CODED_TX_RX_TLIM_DIS CONFIG_BT_CTRL_COEX_PHY_CODED_TX_RX_TLIM_DIS
#define CONFIG_BT_NIMBLE_HCI_EVT_BUF_SIZE CONFIG_BT_NIMBLE_TRANSPORT_EVT_SIZE
#define CONFIG_BT_NIMBLE_HCI_EVT_HI_BUF_COUNT CONFIG_BT_NIMBLE_TRANSPORT_EVT_COUNT
#define CONFIG_BT_NIMBLE_HCI_EVT_LO_BUF_COUNT CONFIG_BT_NIMBLE_TRANSPORT_EVT_DISCARD_COUNT
#define CONFIG_BT_NIMBLE_MSYS1_BLOCK_COUNT CONFIG_BT_NIMBLE_MSYS_1_BLOCK_COUNT
#define CONFIG_BT_NIMBLE_SM_SC_LVL CONFIG_BT_NIMBLE_SM_LVL
#define CONFIG_BT_NIMBLE_TASK_STACK_SIZE CONFIG_BT_NIMBLE_HOST_TASK_STACK_SIZE
#define CONFIG_COMPILER_OPTIMIZATION_DEFAULT CONFIG_COMPILER_OPTIMIZATION_DEBUG
#define CONFIG_COMPILER_OPTIMIZATION_LEVEL_DEBUG CONFIG_COMPILER_OPTIMIZATION_DEBUG
#define CONFIG_CONSOLE_UART CONFIG_ESP_CONSOLE_UART
#define CONFIG_CONSOLE_UART_BAUDRATE CONFIG_ESP_CONSOLE_UART_BAUDRATE
#define CONFIG_CONSOLE_UART_DEFAULT CONFIG_ESP_CONSOLE_UART_DEFAULT
#define CONFIG_CONSOLE_UART_NUM CONFIG_ESP_CONSOLE_UART_NUM
#define CONFIG_ESP32S3_BROWNOUT_DET CONFIG_ESP_BROWNOUT_DET
#define CONFIG_ESP32S3_BROWNOUT_DET_LVL CONFIG_ESP_BROWNOUT_DET_LVL
#define CONFIG_ESP32S3_BROWNOUT_DET_LVL_SEL_7 CONFIG_ESP_BROWNOUT_DET_LVL_SEL_7
#define CONFIG_ESP32S3_DEBUG_OCDAWARE CONFIG_ESP_DEBUG_OCDAWARE
#define CONFIG_ESP32S3_DEEP_SLEEP_WAKEUP_DELAY CONFIG_ESP_SLEEP_WAIT_FLASH_READY_EXTRA_DELAY
#define CONFIG_ESP32S3_DEFAULT_CPU_FREQ_160 CONFIG_ESP_DEFAULT_CPU_FREQ_MHZ_160
#define CONFIG_ESP32S3_DEFAULT_CPU_FREQ_MHZ CONFIG_ESP_DEFAULT_CPU_FREQ_MHZ
#define CONFIG_ESP32S3_RTC_CLK_CAL_CYCLES CONFIG_RTC_CLK_CAL_CYCLES
#define CONFIG_ESP32S3_RTC_CLK_SRC_INT_RC CONFIG_RTC_CLK_SRC_INT_RC
#define CONFIG_ESP32S3_TIME_SYSCALL_USE_RTC_FRC1 CONFIG_NEWLIB_TIME_SYSCALL_USE_RTC_HRT
#define CONFIG_ESP32S3_TIME_SYSCALL_USE_RTC_SYSTIMER CONFIG_NEWLIB_TIME_SYSCALL_USE_RTC_HRT
#define CONFIG_ESP32_APPTRACE_DEST_NONE CONFIG_APPTRACE_DEST_NONE
#define CONFIG_ESP32_APPTRACE_LOCK_ENABLE CONFIG_APPTRACE_LOCK_ENABLE
#define CONFIG_ESP32_DEFAULT_PTHREAD_CORE_NO_AFFINITY CONFIG_PTHREAD_DEFAULT_CORE_NO_AFFINITY
#define CONFIG_ESP32_ENABLE_COREDUMP_TO_NONE CONFIG_ESP_COREDUMP_ENABLE_TO_NONE
#define CONFIG_ESP32_PHY_CALIBRATION_AND_DATA_STORAGE CONFIG_ESP_PHY_CALIBRATION_AND_DATA_STORAGE
#define CONFIG_ESP32_PHY_MAX_TX_POWER CONFIG_ESP_PHY_MAX_TX_POWER
#define CONFIG_ESP32_PHY_MAX_WIFI_TX_POWER CONFIG_ESP_PHY_MAX_WIFI_TX_POWER
#define CONFIG_ESP32_PTHREAD_STACK_MIN CONFIG_PTHREAD_STACK_MIN
#define CONFIG_ESP32_PTHREAD_TASK_CORE_DEFAULT CONFIG_PTHREAD_TASK_CORE_DEFAULT
#define CONFIG_ESP32_PTHREAD_TASK_NAME_DEFAULT CONFIG_PTHREAD_TASK_NAME_DEFAULT
#define CONFIG_ESP32_PTHREAD_TASK_PRIO_DEFAULT CONFIG_PTHREAD_TASK_PRIO_DEFAULT
#define CONFIG_ESP32_PTHREAD_TASK_STACK_SIZE_DEFAULT CONFIG_PTHREAD_TASK_STACK_SIZE_DEFAULT
#define CONFIG_ESP32_WIFI_AMPDU_RX_ENABLED CONFIG_ESP_WIFI_AMPDU_RX_ENABLED
#define CONFIG_ESP32_WIFI_AMPDU_TX_ENABLED CONFIG_ESP_WIFI_AMPDU_TX_ENABLED
#define CONFIG_ESP32_WIFI_DYNAMIC_RX_BUFFER_NUM CONFIG_ESP_WIFI_DYNAMIC_RX_BUFFER_NUM
#define CONFIG_ESP32_WIFI_DYNAMIC_TX_BUFFER CONFIG_ESP_WIFI_DYNAMIC_TX_BUFFER
#define CONFIG_ESP32_WIFI_DYNAMIC_TX_BUFFER_NUM CONFIG_ESP_WIFI_DYNAMIC_TX_BUFFER_NUM
#define CONFIG_ESP32_WIFI_ENABLED CONFIG_ESP_WIFI_ENABLED
#define CONFIG_ESP32_WIFI_ENABLE_WPA3_OWE_STA CONFIG_ESP_WIFI_ENABLE_WPA3_OWE_STA
#define CONFIG_ESP32_WIFI_ENABLE_WPA3_SAE CONFIG_ESP_WIFI_ENABLE_WPA3_SAE
#define CONFIG_ESP32_WIFI_IRAM_OPT CONFIG_ESP_WIFI_IRAM_OPT
#define CONFIG_ESP32_WIFI_MGMT_SBUF_NUM CONFIG_ESP_WIFI_MGMT_SBUF_NUM
#define CONFIG_ESP32_WIFI_NVS_ENABLED CONFIG_ESP_WIFI_NVS_ENABLED
#define CONFIG_ESP32_WIFI_RX_BA_WIN CONFIG_ESP_WIFI_RX_BA_WIN
#define CONFIG_ESP32_WIFI_RX_IRAM_OPT CONFIG_ESP_WIFI_RX_IRAM_OPT
#define CONFIG_ESP32_WIFI_SOFTAP_BEACON_MAX_LEN CONFIG_ESP_WIFI_SOFTAP_BEACON_MAX_LEN
#define CONFIG_ESP32_WIFI_STATIC_RX_BUFFER_NUM CONFIG_ESP_WIFI_STATIC_RX_BUFFER_NUM
#define CONFIG_ESP32_WIFI_SW_COEXIST_ENABLE CONFIG_ESP_COEX_SW_COEXIST_ENABLE
#define CONFIG_ESP32_WIFI_TASK_PINNED_TO_CORE_0 CONFIG_ESP_WIFI_TASK_PINNED_TO_CORE_0
#define CONFIG_ESP32_WIFI_TX_BA_WIN CONFIG_ESP_WIFI_TX_BA_WIN
#define CONFIG_ESP32_WIFI_TX_BUFFER_TYPE CONFIG_ESP_WIFI_TX_BUFFER_TYPE
#define CONFIG_ESP_GRATUITOUS_ARP CONFIG_LWIP_ESP_GRATUITOUS_ARP
#define CONFIG_ESP_SLEEP_DEEP_SLEEP_WAKEUP_DELAY CONFIG_ESP_SLEEP_WAIT_FLASH_READY_EXTRA_DELAY
#define CONFIG_ESP_SYSTEM_PM_POWER_DOWN_CPU CONFIG_PM_POWER_DOWN_CPU_IN_LIGHT_SLEEP
#define CONFIG_ESP_TASK_WDT CONFIG_ESP_TASK_WDT_INIT
#define CONFIG_ESP_WIFI_SW_COEXIST_ENABLE CONFIG_ESP_COEX_SW_COEXIST_ENABLE
#define CONFIG_FLASHMODE_DIO CONFIG_ESPTOOLPY_FLASHMODE_DIO
#define CONFIG_GARP_TMR_INTERVAL CONFIG_LWIP_GARP_TMR_INTERVAL
#define CONFIG_GDBSTUB_MAX_TASKS CONFIG_ESP_GDBSTUB_MAX_TASKS
#define CONFIG_GDBSTUB_SUPPORT_TASKS CONFIG_ESP_GDBSTUB_SUPPORT_TASKS
#define CONFIG_INT_WDT CONFIG_ESP_INT_WDT
#define CONFIG_INT_WDT_CHECK_CPU1 CONFIG_ESP_INT_WDT_CHECK_CPU1
#define CONFIG_INT_WDT_TIMEOUT_MS CONFIG_ESP_INT_WDT_TIMEOUT_MS
#define CONFIG_IPC_TASK_STACK_SIZE CONFIG_ESP_IPC_TASK_STACK_SIZE
#define CONFIG_LOG_BOOTLOADER_LEVEL CONFIG_BOOTLOADER_LOG_LEVEL
#define CONFIG_LOG_BOOTLOADER_LEVEL_INFO CONFIG_BOOTLOADER_LOG_LEVEL_INFO
#define CONFIG_MAIN_TASK_STACK_SIZE CONFIG_ESP_MAIN_TASK_STACK_SIZE
#define CONFIG_MONITOR_BAUD CONFIG_ESPTOOLPY_MONITOR_BAUD
#define CONFIG_NIMBLE_ATT_PREFERRED_MTU CONFIG_BT_NIMBLE_ATT_PREFERRED_MTU
#define CONFIG_NIMBLE_CRYPTO_STACK_MBEDTLS CONFIG_BT_NIMBLE_CRYPTO_STACK_MBEDTLS
#define CONFIG_NIMBLE_ENABLED CONFIG_BT_NIMBLE_ENABLED
#define CONFIG_NIMBLE_GAP_DEVICE_NAME_MAX_LEN CONFIG_BT_NIMBLE_GAP_DEVICE_NAME_MAX_LEN
#define CONFIG_NIMBLE_L2CAP_COC_MAX_NUM CONFIG_BT_NIMBLE_L2CAP_COC_MAX_NUM
#define CONFIG_NIMBLE_MAX_BONDS CONFIG_BT_NIMBLE_MAX_BONDS
#define CONFIG_NIMBLE_MAX_CCCDS CONFIG_BT_NIMBLE_MAX_CCCDS
#define CONFIG_NIMBLE_MAX_CONNECTIONS CONFIG_BT_NIMBLE_MAX_CONNECTIONS
#define CONFIG_NIMBLE_MEM_ALLOC_MODE_INTERNAL CONFIG_BT_NIMBLE_MEM_ALLOC_MODE_INTERNAL
#define CONFIG_NIMBLE_PINNED_TO_CORE CONFIG_BT_NIMBLE_PINNED_TO_CORE
#define CONFIG_NIMBLE_PINNED_TO_CORE_0 CONFIG_BT_NIMBLE_PINNED_TO_CORE_0
#define CONFIG_NIMBLE_ROLE_BROADCASTER CONFIG_BT_NIMBLE_ROLE_BROADCASTER
#define CONFIG_NIMBLE_ROLE_CENTRAL CONFIG_BT_NIMBLE_ROLE_CENTRAL
#define CONFIG_NIMBLE_ROLE_OBSERVER CONFIG_BT_NIMBLE_ROLE_OBSERVER
#define CONFIG_NIMBLE_ROLE_PERIPHERAL CONFIG_BT_NIMBLE_ROLE_PERIPHERAL
#define CONFIG_NIMBLE_RPA_TIMEOUT CONFIG_BT_NIMBLE_RPA_TIMEOUT
#define CONFIG_NIMBLE_SM_LEGACY CONFIG_BT_NIMBLE_SM_LEGACY
#define CONFIG_NIMBLE_SM_SC CONFIG_BT_NIMBLE_SM_SC
#define CONFIG_NIMBLE_SVC_GAP_APPEARANCE CONFIG_BT_NIMBLE_SVC_GAP_APPEARANCE
#define CONFIG_NIMBLE_SVC_GAP_DEVICE_NAME CONFIG_BT_NIMBLE_SVC_GAP_DEVICE_NAME
#define CONFIG_NIMBLE_TASK_STACK_SIZE CONFIG_BT_NIMBLE_HOST_TASK_STACK_SIZE
#define CONFIG_OPTIMIZATION_ASSERTIONS_ENABLED CONFIG_COMPILER_OPTIMIZATION_ASSERTIONS_ENABLE
#define CONFIG_OPTIMIZATION_ASSERTION_LEVEL CONFIG_COMPILER_OPTIMIZATION_ASSERTION_LEVEL
#define CONFIG_OPTIMIZATION_LEVEL_DEBUG CONFIG_COMPILER_OPTIMIZATION_DEBUG
#define CONFIG_PM_POWER_DOWN_TAGMEM_IN_LIGHT_SLEEP CONFIG_PM_RESTORE_CACHE_TAGMEM_AFTER_LIGHT_SLEEP
#define CONFIG_POST_EVENTS_FROM_IRAM_ISR CONFIG_ESP_EVENT_POST_FROM_IRAM_ISR
#define CONFIG_POST_EVENTS_FROM_ISR CONFIG_ESP_EVENT_POST_FROM_ISR
#define CONFIG_SEMIHOSTFS_MAX_MOUNT_POINTS CONFIG_VFS_SEMIHOSTFS_MAX_MOUNT_POINTS
#define CONFIG_SPI_FLASH_WRITING_DANGEROUS_REGIONS_ABORTS CONFIG_SPI_FLASH_DANGEROUS_WRITE_ABORTS
#define CONFIG_STACK_CHECK_NONE CONFIG_COMPILER_STACK_CHECK_MODE_NONE
#define CONFIG_SUPPORT_TERMIOS CONFIG_VFS_SUPPORT_TERMIOS
#define CONFIG_SUPPRESS_SELECT_DEBUG_OUTPUT CONFIG_VFS_SUPPRESS_SELECT_DEBUG_OUTPUT
#define CONFIG_SW_COEXIST_ENABLE CONFIG_ESP_COEX_SW_COEXIST_ENABLE
#define CONFIG_SYSTEM_EVENT_QUEUE_SIZE CONFIG_ESP_SYSTEM_EVENT_QUEUE_SIZE
#define CONFIG_SYSTEM_EVENT_TASK_STACK_SIZE CONFIG_ESP_SYSTEM_EVENT_TASK_STACK_SIZE
#define CONFIG_TASK_WDT CONFIG_ESP_TASK_WDT_INIT
#define CONFIG_TASK_WDT_CHECK_IDLE_TASK_CPU0 CONFIG_ESP_TASK_WDT_CHECK_IDLE_TASK_CPU0
#define CONFIG_TASK_WDT_CHECK_IDLE_TASK_CPU1 CONFIG_ESP_TASK_WDT_CHECK_IDLE_TASK_CPU1
#define CONFIG_TASK_WDT_TIMEOUT_S CONFIG_ESP_TASK_WDT_TIMEOUT_S
#define CONFIG_TCPIP_RECVMBOX_SIZE CONFIG_LWIP_TCPIP_RECVMBOX_SIZE
#define CONFIG_TCPIP_TASK_AFFINITY CONFIG_LWIP_TCPIP_TASK_AFFINITY
#define CONFIG_TCPIP_TASK_AFFINITY_NO_AFFINITY CONFIG_LWIP_TCPIP_TASK_AFFINITY_NO_AFFINITY
#define CONFIG_TCPIP_TASK_STACK_SIZE CONFIG_LWIP_TCPIP_TASK_STACK_SIZE
#define CONFIG_TCP_MAXRTX CONFIG_LWIP_TCP_MAXRTX
#define CONFIG_TCP_MSL CONFIG_LWIP_TCP_MSL
#define CONFIG_TCP_MSS CONFIG_LWIP_TCP_MSS
#define CONFIG_TCP_OVERSIZE_MSS CONFIG_LWIP_TCP_OVERSIZE_MSS
#define CONFIG_TCP_QUEUE_OOSEQ CONFIG_LWIP_TCP_QUEUE_OOSEQ
#define CONFIG_TCP_RECVMBOX_SIZE CONFIG_LWIP_TCP_RECVMBOX_SIZE
#define CONFIG_TCP_SND_BUF_DEFAULT CONFIG_LWIP_TCP_SND_BUF_DEFAULT
#define CONFIG_TCP_SYNMAXRTX CONFIG_LWIP_TCP_SYNMAXRTX
#define CONFIG_TCP_WND_DEFAULT CONFIG_LWIP_TCP_WND_DEFAULT
#define CONFIG_TIMER_QUEUE_LENGTH CONFIG_FREERTOS_TIMER_QUEUE_LENGTH
#define CONFIG_TIMER_TASK_PRIORITY CONFIG_FREERTOS_TIMER_TASK_PRIORITY
#define CONFIG_TIMER_TASK_STACK_DEPTH CONFIG_FREERTOS_TIMER_TASK_STACK_DEPTH
#define CONFIG_TIMER_TASK_STACK_SIZE CONFIG_ESP_TIMER_TASK_STACK_SIZE
#define CONFIG_UDP_RECVMBOX_SIZE CONFIG_LWIP_UDP_RECVMBOX_SIZE
#define CONFIG_WPA_MBEDTLS_CRYPTO CONFIG_ESP_WIFI_MBEDTLS_CRYPTO
#define CONFIG_WPA_MBEDTLS_TLS_CLIENT CONFIG_ESP_WIFI_MBEDTLS_TLS_CLIENT
