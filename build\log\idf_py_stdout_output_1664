-- Existing sdkconfig 'D:/scanner/blecent/sdkconfig' renamed to 'D:/scanner/blecent/sdkconfig.old'.
-- Found Git: D:/Espressif/tools/idf-git/2.44.0/cmd/git.exe (found version "2.44.0.windows.1")
-- The C compiler identification is GNU 14.2.0
-- The CXX compiler identification is GNU 14.2.0
-- The ASM compiler identification is GNU
-- Found assembler: D:/Espressif/tools/xtensa-esp-elf/esp-14.2.0_20241119/xtensa-esp-elf/bin/xtensa-esp32s3-elf-gcc.exe
-- Detecting C compiler ABI info
-- Detecting C compiler ABI info - done
-- Check for working C compiler: D:/Espressif/tools/xtensa-esp-elf/esp-14.2.0_20241119/xtensa-esp-elf/bin/xtensa-esp32s3-elf-gcc.exe - skipped
-- Detecting C compile features
-- Detecting C compile features - done
-- Detecting CXX compiler AB<PERSON> info
-- Detecting CXX compiler AB<PERSON> info - done
-- Check for working CXX compiler: D:/Espressif/tools/xtensa-esp-elf/esp-14.2.0_20241119/xtensa-esp-elf/bin/xtensa-esp32s3-elf-g++.exe - skipped
-- Detecting CXX compile features
-- Detecting CXX compile features - done
-- git rev-parse returned 'fatal: not a git repository (or any of the parent directories): .git'
-- Could not use 'git describe' to determine PROJECT_VER.
-- Building ESP-IDF components for target esp32s3
NOTICE: Using component placed at D:\Espressif\frameworks\esp-idf-v5.4.2\examples\bluetooth\nimble\common\nimble_central_utils for dependency "nimble_central_utils", specified in D:\scanner\blecent\main\idf_component.yml
NOTICE: Updating lock file at D:\scanner\blecent\dependencies.lock
NOTICE: Processing 2 dependencies:
NOTICE: [1/2] nimble_central_utils (*) (D:\Espressif\frameworks\esp-idf-v5.4.2\examples\bluetooth\nimble\common\nimble_central_utils)
NOTICE: [2/2] idf (5.4.2)
-- Project sdkconfig file D:/scanner/blecent/sdkconfig
Loading defaults file D:/scanner/blecent/sdkconfig.defaults...
warning: unknown kconfig symbol 'BTDM_CTRL_MODE_BLE_ONLY' assigned to 'y' in D:/scanner/blecent/sdkconfig.defaults
warning: unknown kconfig symbol 'BTDM_CTRL_MODE_BR_EDR_ONLY' assigned to 'n' in D:/scanner/blecent/sdkconfig.defaults
warning: unknown kconfig symbol 'BTDM_CTRL_MODE_BTDM' assigned to 'n' in D:/scanner/blecent/sdkconfig.defaults
-- Compiler supported targets: xtensa-esp-elf
-- Found Python3: D:/Espressif/python_env/idf5.4_py3.11_env/Scripts/python.exe (found version "3.11.2") found components: Interpreter
-- Performing Test CMAKE_HAVE_LIBC_PTHREAD
-- Performing Test CMAKE_HAVE_LIBC_PTHREAD - Success
-- Found Threads: TRUE
-- Performing Test C_COMPILER_SUPPORTS_WFORMAT_SIGNEDNESS
-- Performing Test C_COMPILER_SUPPORTS_WFORMAT_SIGNEDNESS - Success
-- USING O3
-- App "blecent" version: 1
-- Adding linker script D:/scanner/blecent/build/esp-idf/esp_system/ld/memory.ld
-- Adding linker script D:/scanner/blecent/build/esp-idf/esp_system/ld/sections.ld.in
-- Adding linker script D:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_rom/esp32s3/ld/esp32s3.rom.ld
-- Adding linker script D:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_rom/esp32s3/ld/esp32s3.rom.api.ld
-- Adding linker script D:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_rom/esp32s3/ld/esp32s3.rom.bt_funcs.ld
-- Adding linker script D:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_rom/esp32s3/ld/esp32s3.rom.libgcc.ld
-- Adding linker script D:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_rom/esp32s3/ld/esp32s3.rom.wdt.ld
-- Adding linker script D:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_rom/esp32s3/ld/esp32s3.rom.version.ld
-- Adding linker script D:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_rom/esp32s3/ld/esp32s3.rom.ble_cca.ld
-- Adding linker script D:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_rom/esp32s3/ld/esp32s3.rom.ble_test.ld
-- Adding linker script D:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_rom/esp32s3/ld/esp32s3.rom.newlib.ld
-- Adding linker script D:/Espressif/frameworks/esp-idf-v5.4.2/components/soc/esp32s3/ld/esp32s3.peripherals.ld
-- Components: app_trace app_update bootloader bootloader_support bt cmock console cxx driver efuse esp-tls esp_adc esp_app_format esp_bootloader_format esp_coex esp_common esp_driver_ana_cmpr esp_driver_cam esp_driver_dac esp_driver_gpio esp_driver_gptimer esp_driver_i2c esp_driver_i2s esp_driver_isp esp_driver_jpeg esp_driver_ledc esp_driver_mcpwm esp_driver_parlio esp_driver_pcnt esp_driver_ppa esp_driver_rmt esp_driver_sdio esp_driver_sdm esp_driver_sdmmc esp_driver_sdspi esp_driver_spi esp_driver_touch_sens esp_driver_tsens esp_driver_uart esp_driver_usb_serial_jtag esp_eth esp_event esp_gdbstub esp_hid esp_http_client esp_http_server esp_https_ota esp_https_server esp_hw_support esp_lcd esp_local_ctrl esp_mm esp_netif esp_netif_stack esp_partition esp_phy esp_pm esp_psram esp_ringbuf esp_rom esp_security esp_system esp_timer esp_vfs_console esp_wifi espcoredump esptool_py fatfs freertos hal heap http_parser idf_test ieee802154 json log lwip main mbedtls mqtt newlib nimble_central_utils nvs_flash nvs_sec_provider openthread partition_table perfmon protobuf-c protocomm pthread rt sdmmc soc spi_flash spiffs tcp_transport touch_element ulp unity usb vfs wear_levelling wifi_provisioning wpa_supplicant xtensa
-- Component paths: D:/Espressif/frameworks/esp-idf-v5.4.2/components/app_trace D:/Espressif/frameworks/esp-idf-v5.4.2/components/app_update D:/Espressif/frameworks/esp-idf-v5.4.2/components/bootloader D:/Espressif/frameworks/esp-idf-v5.4.2/components/bootloader_support D:/Espressif/frameworks/esp-idf-v5.4.2/components/bt D:/Espressif/frameworks/esp-idf-v5.4.2/components/cmock D:/Espressif/frameworks/esp-idf-v5.4.2/components/console D:/Espressif/frameworks/esp-idf-v5.4.2/components/cxx D:/Espressif/frameworks/esp-idf-v5.4.2/components/driver D:/Espressif/frameworks/esp-idf-v5.4.2/components/efuse D:/Espressif/frameworks/esp-idf-v5.4.2/components/esp-tls D:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_adc D:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_app_format D:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_bootloader_format D:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_coex D:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_common D:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_driver_ana_cmpr D:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_driver_cam D:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_driver_dac D:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_driver_gpio D:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_driver_gptimer D:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_driver_i2c D:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_driver_i2s D:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_driver_isp D:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_driver_jpeg D:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_driver_ledc D:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_driver_mcpwm D:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_driver_parlio D:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_driver_pcnt D:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_driver_ppa D:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_driver_rmt D:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_driver_sdio D:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_driver_sdm D:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_driver_sdmmc D:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_driver_sdspi D:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_driver_spi D:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_driver_touch_sens D:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_driver_tsens D:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_driver_uart D:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_driver_usb_serial_jtag D:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_eth D:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_event D:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_gdbstub D:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hid D:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_http_client D:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_http_server D:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_https_ota D:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_https_server D:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hw_support D:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_lcd D:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_local_ctrl D:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_mm D:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_netif D:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_netif_stack D:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_partition D:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_phy D:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_pm D:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_psram D:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_ringbuf D:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_rom D:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_security D:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_system D:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_timer D:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_vfs_console D:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_wifi D:/Espressif/frameworks/esp-idf-v5.4.2/components/espcoredump D:/Espressif/frameworks/esp-idf-v5.4.2/components/esptool_py D:/Espressif/frameworks/esp-idf-v5.4.2/components/fatfs D:/Espressif/frameworks/esp-idf-v5.4.2/components/freertos D:/Espressif/frameworks/esp-idf-v5.4.2/components/hal D:/Espressif/frameworks/esp-idf-v5.4.2/components/heap D:/Espressif/frameworks/esp-idf-v5.4.2/components/http_parser D:/Espressif/frameworks/esp-idf-v5.4.2/components/idf_test D:/Espressif/frameworks/esp-idf-v5.4.2/components/ieee802154 D:/Espressif/frameworks/esp-idf-v5.4.2/components/json D:/Espressif/frameworks/esp-idf-v5.4.2/components/log D:/Espressif/frameworks/esp-idf-v5.4.2/components/lwip D:/scanner/blecent/main D:/Espressif/frameworks/esp-idf-v5.4.2/components/mbedtls D:/Espressif/frameworks/esp-idf-v5.4.2/components/mqtt D:/Espressif/frameworks/esp-idf-v5.4.2/components/newlib D:/Espressif/frameworks/esp-idf-v5.4.2/examples/bluetooth/nimble/common/nimble_central_utils D:/Espressif/frameworks/esp-idf-v5.4.2/components/nvs_flash D:/Espressif/frameworks/esp-idf-v5.4.2/components/nvs_sec_provider D:/Espressif/frameworks/esp-idf-v5.4.2/components/openthread D:/Espressif/frameworks/esp-idf-v5.4.2/components/partition_table D:/Espressif/frameworks/esp-idf-v5.4.2/components/perfmon D:/Espressif/frameworks/esp-idf-v5.4.2/components/protobuf-c D:/Espressif/frameworks/esp-idf-v5.4.2/components/protocomm D:/Espressif/frameworks/esp-idf-v5.4.2/components/pthread D:/Espressif/frameworks/esp-idf-v5.4.2/components/rt D:/Espressif/frameworks/esp-idf-v5.4.2/components/sdmmc D:/Espressif/frameworks/esp-idf-v5.4.2/components/soc D:/Espressif/frameworks/esp-idf-v5.4.2/components/spi_flash D:/Espressif/frameworks/esp-idf-v5.4.2/components/spiffs D:/Espressif/frameworks/esp-idf-v5.4.2/components/tcp_transport D:/Espressif/frameworks/esp-idf-v5.4.2/components/touch_element D:/Espressif/frameworks/esp-idf-v5.4.2/components/ulp D:/Espressif/frameworks/esp-idf-v5.4.2/components/unity D:/Espressif/frameworks/esp-idf-v5.4.2/components/usb D:/Espressif/frameworks/esp-idf-v5.4.2/components/vfs D:/Espressif/frameworks/esp-idf-v5.4.2/components/wear_levelling D:/Espressif/frameworks/esp-idf-v5.4.2/components/wifi_provisioning D:/Espressif/frameworks/esp-idf-v5.4.2/components/wpa_supplicant D:/Espressif/frameworks/esp-idf-v5.4.2/components/xtensa
-- Configuring done (14.2s)
-- Generating done (1.8s)
-- Build files have been written to: D:/scanner/blecent/build
